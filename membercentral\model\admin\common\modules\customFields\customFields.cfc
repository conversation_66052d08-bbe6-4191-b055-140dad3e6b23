<cfcomponent output="false">

	<cffunction name="getGridHTML" access="public" output="false" returntype="struct">
		<cfargument name="arrGridData" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { js="", html="", idExt=timeFormat(now(),'hhmmssl') }>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=resourceCustomFields&mca_ajaxfunc=">

		<cfsavecontent variable="local.strReturn.js">
			<cfoutput>
			<script type="text/javascript">
				<cfloop array="#arguments.arrGridData#" index="local.thisGrid">
					<cfif len(local.thisGrid.gridExt)>
						<cfset local.thisGridJS = getGridJS(strGridData=local.thisGrid, baseURL=local.baseURL)>
						#local.thisGridJS# 
					</cfif>
				</cfloop>
			</script>
			<style>
				<cfloop array="#arguments.arrGridData#" index="local.thisGrid">
					<cfif len(local.thisGrid.gridExt)>#getGridCSS(strGridData=local.thisGrid)#</cfif>
				</cfloop>
			</style>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.html">
			<cfoutput>
			<div id="mccf_div_grids#local.strReturn.idExt#" class="mccf_div_gridsParent" data-idExt="#local.strReturn.idExt#">
				<cfloop array="#arguments.arrGridData#" index="local.thisGrid">
					<cfif len(local.thisGrid.gridExt)>
						<cfset local.thisGridHTML = getGridBoxHTML(strGridData=local.thisGrid)>
						#local.thisGridHTML# 
					<cfelseif structKeyExists(local.thisGrid,"nonGridHTML")>
						#local.thisGrid.nonGridHTML# 
					</cfif>
				</cfloop>
			</div>
			<div id="mccf_div_frmContainer#local.strReturn.idExt#" class="d-none"></div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getGridJS" access="public" output="false" returntype="string">
		<cfargument name="strGridData" type="struct" required="true">
		<cfargument name="baseURL" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.js">
			<cfoutput>
				<cfset local.fieldsListURL = "/?mode=stream&pg=admin&mca_jsonlib=mcdatatable&com=customFieldsJSON&meth=getFields&csrid=#arguments.strGridData.controllingSRID#&usageRT=#arguments.strGridData.resourceType#&usageAN=#arguments.strGridData.areaName#&gext=#arguments.strGridData.gridext#">
				<cfset local.addFieldURL = "#arguments.baseURL.replace('mode=stream','mode=direct')#addField&_cfcsrid=#arguments.strGridData.controllingSRID#&_cfur=#arguments.strGridData.resourceType#&_cfua=#arguments.strGridData.areaName#&_cfg=#arguments.strGridData.gridext#">
				<cfset local.editFieldURL = "#arguments.baseURL.replace('mode=stream','mode=direct')#editField&_cfcsrid=#arguments.strGridData.controllingSRID#&_cfg=#arguments.strGridData.gridext#">
				<cfset local.editFieldValueURL = "#arguments.baseURL.replace('mode=stream','mode=direct')#editFieldValue&_cfcsrid=#arguments.strGridData.controllingSRID#&_cfg=#arguments.strGridData.gridext#">
				<cfset local.editFieldGroupingURL = "#arguments.baseURL.replace('mode=stream','mode=direct')#editFieldGrouping&_cfcsrid=#arguments.strGridData.controllingSRID#&_cfg=#arguments.strGridData.gridext#">
				<cfif structKeyExists(arguments.strGridData,"detailID")>
					<cfset local.fieldsListURL = "#local.fieldsListURL#&detailID=#arguments.strGridData.detailID#">
					<cfset local.addFieldURL = "#local.addFieldURL#&_cfdtid=#arguments.strGridData.detailID#">
					<cfset local.editFieldURL = "#local.editFieldURL#&_cfdtid=#arguments.strGridData.detailID#">
					<cfset local.editFieldGroupingURL = "#local.editFieldGroupingURL#&_cfdtid=#arguments.strGridData.detailID#">
				</cfif>

				var mccf_#arguments.strGridData.gridExt#_table;
				var #toScript(local.fieldsListURL,"mccf_#arguments.strGridData.gridExt#_sourceURL")#
				var #toScript(local.addFieldURL,"mccf_addField#arguments.strGridData.gridext#URL")#
				var #toScript(local.editFieldURL,"mccf_editField#arguments.strGridData.gridext#URL")#
				var #toScript(local.editFieldValueURL,"mccf_editFieldValue#arguments.strGridData.gridext#URL")#
				var #toScript(local.editFieldGroupingURL,"mccf_editFieldGrouping#arguments.strGridData.gridext#URL")#
				var #toScript("Field","mccf_#arguments.strGridData.gridExt#_fieldLabel")#
				var #toScript("Fields","mccf_#arguments.strGridData.gridExt#_fieldLabelPlural")#
				var #toScript("Option","mccf_#arguments.strGridData.gridExt#_fieldOptionLabel")#
				var #toScript("Options","mccf_#arguments.strGridData.gridExt#_fieldOptionLabelPlural")#
				var mccf_#arguments.strGridData.gridExt#_readonly = #booleanFormat(structKeyExists(arguments.strGridData,'readOnly') AND arguments.strGridData.readOnly EQ 1)#;

				$(function(){
					<cfif arguments.strGridData.initGridOnLoad>
						mccf_initFieldsTable('#arguments.strGridData.gridExt#');
					</cfif>
					<!--- notify control app that the resource field app is ready --->
					parent.postMessage({ success:true, messagetype:'MCCFResourceAppLoadEvent', gridext:'#arguments.strGridData.gridExt#' },'*');
				});
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.js>
	</cffunction>

	<cffunction name="getGridCSS" access="public" output="false" returntype="string">
		<cfargument name="strGridData" type="struct" required="true">

		<cfset var gridCSS = "">

		<cfsavecontent variable="gridCSS">
			<cfoutput>
			<cfif structKeyExists(arguments.strGridData,"gridMode") and arguments.strGridData.gridMode eq "compact">
				table##mccf_#arguments.strGridData.gridExt#_table { font-size: .8rem; }
				table##mccf_#arguments.strGridData.gridExt#_table td, table##mccf_#arguments.strGridData.gridExt#_table th {padding: 0.15rem;}
				table##mccf_#arguments.strGridData.gridExt#_table a.btn-xs { padding-top: 0!important;padding-bottom: 0!important; }
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn gridCSS>
	</cffunction>

	<cffunction name="getGridBoxHTML" access="public" output="false" returntype="string">
		<cfargument name="strGridData" type="struct" required="true">

		<cfset var gridBoxHTML = "">
		<cfset var readOnly = structKeyExists(arguments.strGridData,'readOnly') AND arguments.strGridData.readOnly EQ 1>

		<cfsavecontent variable="gridBoxHTML">
			<cfoutput>
			<cfif structKeyExists(arguments.strGridData,'header') and len(arguments.strGridData.header)>
				#arguments.strGridData.header#
			</cfif>
			<div<cfif structKeyExists(arguments.strGridData,'gridID')> id="#arguments.strGridData.gridID#"</cfif> class="mccf_div_gridContainer <cfif structKeyExists(arguments.strGridData,'gridClassList')>#replace(arguments.strGridData.gridClassList,',',' ','all')#</cfif>" data-gridExt="#arguments.strGridData.gridext#">
				<cfif structKeyExists(arguments.strGridData,"title") and len(arguments.strGridData.title)>
					<h5>#arguments.strGridData.title#</h5>
				</cfif>
				<cfif structKeyExists(arguments.strGridData,'intro') and len(arguments.strGridData.intro)>
					<div class="row mb-2">
						<div class="col">#arguments.strGridData.intro#</div>
					</div>
				</cfif>
				<div class="mx-2">
					<cfif NOT readOnly>
						<div class="row">
							<div class="col">
								<button type="button" class="<cfif structKeyExists(arguments.strGridData,'btnClassList')>#replace(arguments.strGridData.btnClassList,',',' ','all')#<cfelse>btn btn-sm btn-secondary</cfif>" id="mccf_btn_addField" onclick="mccf_addField('#arguments.strGridData.gridext#')">
									<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
									<span class="btn-wrapper--label">Add Field</span>
								</button>
							</div>
						</div>
					</cfif>
					<table id="mccf_#arguments.strGridData.gridExt#_table" class="table table-sm table-bordered table-hover" style="width:100%;">
						<thead>
							<tr>
								<th>Fields</th>
								<cfif NOT readOnly>
									<th>Actions</th>
								</cfif>
							</tr>
						</thead>
					</table> 
				</div>
			</div>
			<cfif structKeyExists(arguments.strGridData,"footer") and len(arguments.strGridData.footer)>
				#arguments.strGridData.footer#
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn gridBoxHTML>
	</cffunction>

	<cffunction name="addField" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageRT" type="string" required="true">
		<cfargument name="usageAN" type="string" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="gridExt" type="string" required="true">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryFieldUsages = getFieldUsageDetails(usageRT=arguments.usageRT, usageAN=arguments.usageAN)>

		<cfif local.qryFieldUsages.recordCount is 1>
			<cfset local.data = editField(siteID=arguments.siteID, orgID=arguments.orgID, fieldID=0, csrid=arguments.csrid, usageID=local.qryFieldUsages.usageID, detailID=arguments.detailID, gridExt=arguments.gridExt, event=arguments.event)>
		<cfelseif local.qryFieldUsages.recordCount gt 1>
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_selectFieldUsage.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "No field usages found.">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editField" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="gridExt" type="string" required="true">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryCustomField = getFieldDetails(csrid=arguments.csrid, fieldID=arguments.fieldID, usageID=arguments.usageID)>
		<cfset local.qryCustomFieldTypes = getCustomFieldTypes(usageID=arguments.usageID)>
		<cfset local.qryFieldUsage = getFieldUsage(usageID=arguments.usageID)>
		<cfset local.fieldGroupingParentUsageID = val(local.qryFieldUsage.parentUsageID) GT 0 ? local.qryFieldUsage.parentUsageID : local.qryFieldUsage.usageID>
		<cfset local.qryFieldGroupings = getFieldGroupings(csrid=arguments.csrid, usageID=local.fieldGroupingParentUsageID, detailID=arguments.detailID)>

		<!--- show currency types --->
		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = " #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultCurrencyType#">
		</cfif>

		<cfif local.qryFieldUsage.parentUsageID is 1>
			<cfset local.qryEventScheduleMappedPrice = CreateObject("component","model.admin.events.event").getEventFieldScheduleMappedPrice(siteResourceID=arguments.csrid, fieldID=arguments.fieldID)>
		</cfif>

		<cfset local.GLAccountPath = "">
		<cfif val(local.qryCustomField.GLAccountID) gt 0>
			<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryCustomField.GLAccountID, orgID=arguments.orgID)>
			<cfset local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded>
		</cfif>

		<!--- set account type to revenue --->
		<cfset arguments.event.setValue('glatid',3)>
		<cfset arguments.event.setValue('selectFN','mccf_selectGLAccountResult')>
		<cfset local.showGLSelector = CreateObject("component","model.admin.GLAccounts.GLAccountsAdmin").showSelector(event=arguments.event)>

		<cfif local.qryFieldUsage.allowBranching is 1>
			<cfswitch expression="#local.qryFieldUsage.areaName#">
				<cfcase value="ReferralPanelChooser">
					<cfset local.strBranchFieldFilters = getBranchFieldFilterSelector(siteID=arguments.siteID, csrid=arguments.csrid, resourceType='ClientReferrals', areaName='ReferralPanelChooser', fieldID=arguments.fieldID, fieldLabel='Question', gridExt=arguments.gridExt)>
				</cfcase>
				<cfdefaultcase>
					<cfset local.strBranchFieldFilters = { qryFields=queryNew(''), arrFieldConditions=arrayNew(1), fieldSelectArea="", fieldSelectControls="" }>
				</cfdefaultcase>
			</cfswitch>
		</cfif>

		<cfset local.hasFtdImageFieldTypeSupport = QueryFilter(local.qryCustomFieldTypes,
														function(thisRow) {
															return arguments.thisRow.displayTypeCode EQ 'FEATUREDIMG';
														}).recordCount IS 1>
		<cfif local.hasFtdImageFieldTypeSupport>
			<cfset local.qryFeaturedImageConfigs = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getFeaturedImageConfigsForSite(siteID=arguments.siteID, excludeEmpty=1)>
			<cfset local.qryFeaturedImageConfigs.sort("isPlatformWide","desc")>
			<cfset local.qrySiteFeaturedImageConfigs = local.qryFeaturedImageConfigs.filter(
															function(thisRow) { 
																return arguments.thisRow.isPlatformWide eq 0; 
															})>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editField.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editFieldValue" access="public" output="false" returntype="string">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="gridExt" type="string" required="true">

		<cfset var local = structNew()>

		<!--- show currency types --->
		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = " #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultCurrencyType#">
		</cfif>

		<cfset local.qryCustomField = getFieldDetails(csrid=arguments.csrid, fieldID=arguments.fieldID, usageID=arguments.usageID)>
		<cfset local.qryFieldValue = getFieldOptions(fieldID=arguments.fieldID, restrictToValueIDList=arguments.valueID)>

		<cfif local.qryCustomField.parentUsageID is 1>
			<cfset local.qryFldValScheduleMappedPrice = CreateObject("component","model.admin.events.event").getEvFieldOptionScheduleMappedPrice(siteResourceID=arguments.csrid, valueID=arguments.valueID)>
		</cfif>

		<cfif local.qryCustomField.allowBranching is 1>
			<cfswitch expression="#local.qryCustomField.areaName#">
				<cfcase value="ReferralPanelChooser">
					<cfset local.objAdminReferrals = CreateObject("component","model.admin.referrals.referrals")>
					<cfset local.panelID = local.objAdminReferrals.getReferralPanelIDFromSiteResourceID(siteResourceID=arguments.csrid)>
					<cfset local.qrySiteResources = local.objAdminReferrals.getReferralPanelsForResourceFields(siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID, panelID=local.panelID)>
					<cfset local.qryFieldValueSiteResources = getFieldValueSiteResources(valueID=val(local.qryFieldValue.valueID))>

					<cfif NOT val(local.panelID)>
						<cfset local.strLabel = { resourceLabel = "Panel", resourceLabelLabelPlural = "Panels" , isValueMultiple= false }>
					<cfelse>
						<cfset local.strLabel = { resourceLabel = "Panel", resourceLabelLabelPlural = "Panels" }>
					</cfif>
				</cfcase>
				<cfdefaultcase>
					<cfset local.qrySiteResources = queryNew('')>
					<cfset local.qryFieldValueSiteResources = queryNew('')>
					<cfset local.strLabel = { resourceLabel = "Option", resourceLabelPlural = "Options" }>
				</cfdefaultcase>
			</cfswitch>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editFieldValue.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editFieldGrouping" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="fieldGroupingID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="gridExt" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.qryFieldGrouping = getFieldGrouping(fieldGroupingID=arguments.fieldGroupingID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editFieldGrouping.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFieldUsage" access="private" output="false" returntype="query">
		<cfargument name="usageID" type="numeric" required="true">

		<cfset var qryFieldUsage = "">
		
		<cfquery name="qryFieldUsage" datasource="#application.dsn.membercentral.dsn#">
			select usageID, areaName, areaDesc, parentUsageID, offerDisplayOnly, allowBranching
			from dbo.cf_fieldUsages
			where usageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">
		</cfquery>

		<cfreturn qryFieldUsage>
	</cffunction>

	<cffunction name="getFieldUsageDetails" access="public" output="false" returntype="query">
		<cfargument name="usageRT" type="string" required="true">
		<cfargument name="usageAN" type="string" required="true">

		<cfset var qryFieldUsages = "">
		
		<cfquery name="qryFieldUsages" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int;
			select @usageID = dbo.fn_cf_getUsageID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageRT#">, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageAN#">, NULL);

			with fieldUsages as (
				select usageID, areaName, areaDesc, allowBranching
				from dbo.cf_fieldUsages
				where usageID = @usageID
				and parentUsageID is null
					union all
				select cfu.usageID, cfu.areaName, cfu.areaDesc, cfu.allowBranching
				from dbo.cf_fieldUsages as cfu
				inner join fieldUsages as pfu on pfu.usageID = cfu.parentUsageID
			)

			select fu.usageID, fu.areaDesc, fu.areaName, fu.allowBranching
			from fieldUsages as fu
			where exists (select 1 from dbo.cf_fieldUsageFieldTypes where usageID = fu.usageID);
		</cfquery>

		<cfreturn qryFieldUsages>
	</cffunction>

	<cffunction name="getFieldDetails" access="public" output="false" returntype="query">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		
		<cfset var qryCustomField = "">
		
		<cfquery name="qryCustomField" datasource="#application.dsn.membercentral.dsn#">
			select f.fieldID, f.fieldTypeID, f.fieldText, f.uid, f.fieldReference, f.adminOnly, f.displayOnly, f.GLAccountID, f.enableConditions, 
				f.amount, f.inventory, f.isRequired, f.requiredMsg, f.autoFillRegistrant, f.featureImageConfigID, f.isActive, ft.dataTypeCode, 
				ft.dataType, ft.displayTypeCode, ft.displayType, ft.fieldTypeCode, ft.dataTypeDisplayAs, ft.supportQty, ft.supportAmt, 
				fu.parentUsageID, fu.allowBranching, fu.areaName, f.fieldGroupingID
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			where f.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and f.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">
			and f.usageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">
		</cfquery>
		
		<cfreturn qryCustomField>
	</cffunction>

	<cffunction name="getFieldValueSiteResources" access="public" output="false" returntype="query">
		<cfargument name="valueID" type="numeric" required="true">
		
		<cfset var qryFieldValueSiteResources = "">
		
		<cfquery name="qryFieldValueSiteResources" datasource="#application.dsn.membercentral.dsn#">
			SELECT valueID, siteResourceID
			FROM dbo.cf_fieldValueSiteResources
			WHERE valueID = <cfqueryparam value="#arguments.valueID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryFieldValueSiteResources>
	</cffunction>

	<cffunction name="saveFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="yes">
		<cfargument name="fieldValue" type="string" required="yes">
		<cfargument name="amount" type="string" required="yes">
		<cfargument name="inventory" type="string" required="yes">
		<cfargument name="siteResourceIDList" type="string" required="true">

		<cfscript>
		var local = structNew();
		local.returnStruct = structNew();

		if (arguments.valueID is 0) {
			local.returnStruct.valueID = createFieldValue(fieldID=arguments.fieldID, parentUsageID=arguments.parentUsageID, fieldValue=arguments.fieldValue, amount=arguments.amount, inventory=arguments.inventory, siteResourceIDList=arguments.siteResourceIDList);
		} else {
			updateFieldValue(parentUsageID=arguments.parentUsageID, valueID=valueID, fieldValue=arguments.fieldValue, amount=arguments.amount, inventory=arguments.inventory, siteResourceIDList=arguments.siteResourceIDList);
		}
		local.returnStruct.success = true;
		</cfscript>	

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createFieldValue" access="private" output="false" returntype="numeric">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="fieldValue" type="string" required="yes">
		<cfargument name="amount" type="string" required="yes">
		<cfargument name="inventory" type="string" required="yes">
		<cfargument name="siteResourceIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.valueID = 0>

		<cfif len(arguments.fieldValue)>
			<cfif arguments.parentUsageID is 1>
				<cfset local.arrPriceTypes = arrayNew(1)>
				<cfset local.arrScheduleAmt = listToArray(arguments.amount,'|')>
				<cfloop array="#local.arrScheduleAmt#" index="local.thisScheduleAmt">
					<cfset local.strTemp = { scheduleID=getToken(local.thisScheduleAmt,1,"_"), amount=val(ReReplace(getToken(local.thisScheduleAmt,2,"_"),'[^0-9\.]','','ALL')) }>
					<cfset arrayAppend(local.arrPriceTypes,local.strTemp)>
				</cfloop>
				<cfset arguments.amount = 0>
			</cfif>

			<cfset arguments.amount = val(ReReplace(arguments.amount,'[^0-9\.]','','ALL'))>

			<cfstoredproc procedure="cf_createFieldValue" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.fieldValue#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">
				<cfif val(arguments.inventory) gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.valueID">
			</cfstoredproc>
		</cfif>

		<cfif local.valueID gt 0 and arguments.parentUsageID is 1 and structKeyExists(local,"arrPriceTypes") and arrayLen(local.arrPriceTypes)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertCustomPriceDetails">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @scheduleID int, @valueID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.valueID#">;
					DECLARE @tmpSchedulePrices TABLE (scheduleID int PRIMARY KEY, amount decimal(18,2));

					<cfloop array="#local.arrPriceTypes#" index="local.thisType">
						SET @scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisType.scheduleID#">;
						
						IF NOT EXISTS (SELECT 1 FROM @tmpSchedulePrices WHERE scheduleID = @scheduleID)
							INSERT INTO @tmpSchedulePrices (scheduleID, amount)
							VALUES (@scheduleID, <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.thisType.amount#">);
					</cfloop>

					INSERT INTO dbo.cf_fieldValuesAvailable_usage1 (valueID, scheduleID, amount)
					SELECT @valueID, scheduleID, amount
					FROM @tmpSchedulePrices;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfelseif local.valueID gt 0 and len(arguments.siteResourceIDList)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAssociateSiteResources">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @valueID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.valueID#">;
					
					BEGIN TRAN;
						INSERT INTO dbo.cf_fieldValueSiteResources (valueID, siteResourceID)
						select @valueID, listItem
						from dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteResourceIDList#">,',');
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
		
		<cfreturn local.valueID>
	</cffunction>
	
	<cffunction name="updateFieldValue" access="private" output="false" returntype="void">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="yes">
		<cfargument name="fieldValue" type="string" required="yes">
		<cfargument name="amount" type="string" required="yes">
		<cfargument name="inventory" type="string" required="yes">
		<cfargument name="siteResourceIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfif arguments.parentUsageID is 1>
			<cfset local.arrPriceTypes = arrayNew(1)>
			<cfset local.arrScheduleAmt = listToArray(arguments.amount,'|')>
			<cfloop array="#local.arrScheduleAmt#" index="local.thisScheduleAmt">
				<cfset local.strTemp = { scheduleID=getToken(local.thisScheduleAmt,1,"_"), amount=val(ReReplace(getToken(local.thisScheduleAmt,2,"_"),'[^0-9\.]','','ALL')) }>
				<cfset arrayAppend(local.arrPriceTypes,local.strTemp)>
			</cfloop>
			<cfset arguments.amount = 0>
		</cfif>

		<cfset arguments.amount = val(ReReplace(arguments.amount,'[^0-9\.]','','ALL'))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @valueID int, @fieldValue varchar(max), @amount decimal(18,2), @inventory int;
				SET @valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">;
				SET @fieldValue = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.fieldValue#">;
				SET @amount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.amount#">;
				SET @inventory = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#">,0);

				<cfif structKeyExists(local,"arrPriceTypes") and arrayLen(local.arrPriceTypes)>
					DECLARE @scheduleID int;
					DECLARE @tmpSchedulePrices TABLE (scheduleID int PRIMARY KEY, amount decimal(18,2));

					<cfloop array="#local.arrPriceTypes#" index="local.thisType">
						SET @scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisType.scheduleID#">;
						
						IF NOT EXISTS (SELECT 1 FROM @tmpSchedulePrices WHERE scheduleID = @scheduleID)
							INSERT INTO @tmpSchedulePrices (scheduleID, amount)
							VALUES (@scheduleID, <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.thisType.amount#">);
					</cfloop>
				</cfif>

				BEGIN TRAN;
					EXEC dbo.cf_updateFieldValue @valueID=@valueID, @fieldValue=@fieldValue, @amount=@amount, @inventory=@inventory;

					<cfif arguments.parentUsageID is 1>
						DELETE FROM dbo.cf_fieldValuesAvailable_usage1
						WHERE valueID = @valueID;

						<cfif structKeyExists(local,"arrPriceTypes") and arrayLen(local.arrPriceTypes)>
							INSERT INTO dbo.cf_fieldValuesAvailable_usage1 (valueID, scheduleID, amount)
							SELECT @valueID, scheduleID, amount
							FROM @tmpSchedulePrices;
						</cfif>
					</cfif>

					DELETE FROM dbo.cf_fieldValueSiteResources
					WHERE valueID = @valueID;

					<cfif len(arguments.siteResourceIDList)>
						INSERT INTO dbo.cf_fieldValueSiteResources (valueID, siteResourceID)
						select @valueID, listItem
						from dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteResourceIDList#">,',');
					</cfif>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="saveFieldGrouping" access="public" output="false" returntype="struct">
		<cfargument name="fieldGroupingID" type="numeric" required="true">
		<cfargument name="fieldGrouping" type="string" required="true">
		<cfargument name="fieldGroupingDesc" type="string" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		
		<cfset var qryUpdateFieldGrouping = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUpdateFieldGrouping">
			SET NOCOUNT ON;

			DECLARE @fieldGroupingID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">,
				@controllingSiteResourceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">,
				@usageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">,
				@detailID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">,
				@fieldGrouping varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldGrouping#">,
				@fieldGroupingDesc varchar(400) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldGroupingDesc#">,
				@parentUsageID int;

			SELECT @parentUsageID = CASE WHEN parentUsageID IS NOT NULL THEN parentUsageID ELSE usageID END
			FROM dbo.cf_fieldUsages 
			WHERE usageID = @usageID;

			IF NOT EXISTS (SELECT 1 FROM dbo.cf_fieldsGrouping WHERE fieldControllingSiteResourceID = @controllingSiteResourceID 
							AND fieldUsageID = @parentUsageID AND ISNULL(fieldDetailID,0) = ISNULL(@detailID,0) 
							AND fieldGrouping = @fieldGrouping AND fieldGroupingID <> @fieldGroupingID)
				UPDATE dbo.cf_fieldsGrouping
				SET fieldGrouping = @fieldGrouping,
					fieldGroupingDesc = @fieldGroupingDesc
				WHERE fieldGroupingID = @fieldGroupingID;
		</cfquery>

		<cfreturn { 'success':true }>
	</cffunction>

	<cffunction name="getCustomFieldTypes" access="public" output="false" returntype="query">
		<cfargument name="usageID" type="numeric" required="true">

		<cfset var qryCustomFieldTypes = "">

		<cfquery name="qryCustomFieldTypes" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int;
			set @usageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">;

			select distinct ft.displayTypeCode,	ft.displayType, 
				(select '''' + ft1.fieldTypeCode+'|'+ft1.dataTypeDisplayAs + ''','
					from dbo.cf_fieldTypes as ft1
					inner join dbo.cf_fieldUsageFieldTypes as fuft1 on fuft1.fieldTypeID = ft1.fieldTypeID
					inner join dbo.cf_fieldUsages as fu1 on fu1.usageID = fuft1.usageID
					where fuft1.usageID = @usageID
					and ft1.displayTypeCode = ft.displayTypeCode
					order by ft1.dataTypeDisplayAs
					FOR XML PATH('')) as fieldTypeCodes,
				(select ft2.fieldTypeCode + '|'
					from dbo.cf_fieldTypes as ft2
					inner join dbo.cf_fieldUsageFieldTypes as fuft2 on fuft2.fieldTypeID = ft2.fieldTypeID
					inner join dbo.cf_fieldUsages as fu2 on fu2.usageID = fuft2.usageID
					where fuft2.usageID = @usageID
					and ft2.displayTypeCode = ft.displayTypeCode
					and ft2.supportQty = 1
					FOR XML PATH('')) as supportQtyFieldCodes,
				(select ft3.fieldTypeCode + '|'
					from dbo.cf_fieldTypes as ft3
					inner join dbo.cf_fieldUsageFieldTypes as fuft3 on fuft3.fieldTypeID = ft3.fieldTypeID
					inner join dbo.cf_fieldUsages as fu3 on fu3.usageID = fuft3.usageID
					where fuft3.usageID = @usageID
					and ft3.displayTypeCode = ft.displayTypeCode
					and ft3.supportAmt = 1
					FOR XML PATH('')) as supportAmtFieldCodes
			from dbo.cf_fieldTypes as ft
			inner join dbo.cf_fieldUsageFieldTypes as fuft on fuft.fieldTypeID = ft.fieldTypeID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = fuft.usageID
			where fuft.usageID = @usageID
			order by ft.displayTypeCode
		</cfquery>
		
		<cfreturn qryCustomFieldTypes>
	</cffunction>

	<cffunction name="getFieldGrouping" access="private" output="false" returntype="query">
		<cfargument name="fieldGroupingID" type="numeric" required="true">

		<cfset var qryFieldGrouping = "">

		<cfquery name="qryFieldGrouping" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT fieldGroupingID, fieldGrouping, fieldGroupingDesc, fieldGroupingOrder
			FROM dbo.cf_fieldsGrouping
			WHERE fieldGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryFieldGrouping>
	</cffunction>

	<cffunction name="getFieldGroupings" access="private" output="false" returntype="query">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">

		<cfset var qryFieldGroupings = "">

		<cfquery name="qryFieldGroupings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT fieldGroupingID, fieldGrouping, fieldGroupingOrder
			FROM dbo.cf_fieldsGrouping
			WHERE fieldControllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">
			AND fieldUsageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">
			<cfif arguments.detailID gt 0>
				AND fieldDetailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			</cfif>
			ORDER BY fieldGroupingOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryFieldGroupings>
	</cffunction>

	<cffunction name="getFieldUsageFieldGroupings" access="public" output="false" returntype="query">
		<cfargument name="usageRT" type="string" required="true">
		<cfargument name="usageAN" type="string" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">

		<cfset var qryFieldUsages = "">
		
		<cfquery name="qryFieldUsages" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageID int, @parentUsageID int;
			SELECT @usageID = dbo.fn_cf_getUsageID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageRT#">, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageAN#">, NULL);

			SELECT @parentUsageID = CASE WHEN parentUsageID IS NOT NULL THEN parentUsageID ELSE usageID END
			FROM dbo.cf_fieldUsages 
			WHERE usageID = @usageID;

			SELECT fieldGroupingID, fieldGrouping, fieldGroupingDesc, fieldGroupingOrder
			FROM dbo.cf_fieldsGrouping
			WHERE fieldControllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">
			AND fieldUsageID = @parentUsageID
			<cfif arguments.detailID gt 0>
				AND fieldDetailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			</cfif>
			ORDER BY fieldGroupingOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryFieldUsages>
	</cffunction>

	<cffunction name="doesCustomFieldExists" access="public" output="false" returntype="struct">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="fieldReference" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qryCheckFieldExists" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
	
			declare @siteResourceID int, @fieldID int, @fieldReference varchar(128), @usageID int, @detailID int;
			set @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
			set @fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">;
			set @fieldReference = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldReference#">;
			set @usageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">;
			set @detailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">;
			
			IF EXISTS (select 1 from dbo.cf_fields where controllingSiteResourceID=@siteResourceID and fieldReference=@fieldReference and isActive=1 and usageID = @usageID and isnull(detailID,0)=isnull(@detailID,0) and fieldID<>@fieldID) 
				select 1 as columnExists;
			ELSE
				select 0 as columnExists;
		</cfquery>

		<cfif local.qryCheckFieldExists.columnExists is 1>
			<cfset local.data.columnExists = true>
		<cfelse>
			<cfset local.data.columnExists = false>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveCustomField" access="public" output="false" returntype="struct">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="fldID" type="numeric" required="true">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="fldTypeCode" type="string" required="true">
		<cfargument name="fldText" type="string" required="true">
		<cfargument name="fldRef" type="string" required="true">
		<cfargument name="adminOnly" type="boolean" required="true">
		<cfargument name="dispOnly" type="boolean" required="true">
		<cfargument name="autoFillReg" type="boolean" required="true">
		<cfargument name="isReq" type="boolean" required="true">
		<cfargument name="reqMsg" type="string" required="true">
		<cfargument name="inventory" type="string" required="true">
		<cfargument name="amt" type="string" required="true">
		<cfargument name="glID" type="string" required="true">
		<cfargument name="ftdImgConfigID" type="string" required="true">
		<cfargument name="fldUID" type="string" required="true">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="enableConditions" type="boolean" required="true">
		<cfargument name="strCondition" type="string" required="true">
		<cfargument name="fldGrping" type="string" required="true">
		<cfargument name="newFldGrpingName" type="string" required="true">
		<cfargument name="newFldGrpingDesc" type="string" required="true">

		<cfscript>
		var local = structNew();
		local.returnStruct = structNew();
		local.returnStruct.fieldID = arguments.fldID;

		if (arguments.fldGrping EQ 'new' AND len(trim(arguments.newFldGrpingName))) {
			local.fieldGroupingID = addFieldGrouping(siteResourceID=arguments.csrid, usageID=arguments.usageID, detailID=arguments.detailID, fieldGroupingName=arguments.newFldGrpingName, fieldGroupingDesc=arguments.newFldGrpingDesc);
		} else {
			local.fieldGroupingID = val(arguments.fldGrping);
		}

		if (arguments.fldID gt 0) {
			updateCustomField(siteResourceID=arguments.csrid, fieldID=arguments.fldID, usageID=arguments.usageID, detailID=arguments.detailID, fieldTypeCode=arguments.fldTypeCode, 
											fieldText=arguments.fldText, fieldReference=arguments.fldRef, adminOnly=arguments.adminOnly, dispOnly=arguments.dispOnly, 
											autoFillReg=arguments.autoFillReg, isRequired=arguments.isReq, requiredMsg=arguments.reqMsg, inventory=arguments.inventory, 
											amount=arguments.amt, GLAccountID=arguments.glID, ftdImgConfigID=arguments.ftdImgConfigID, fieldUID=arguments.fldUID, 
											parentUsageID=arguments.parentUsageID, fieldGroupingID=local.fieldGroupingID);
			local.returnStruct.thisaction = "update";
		} else {
			local.returnStruct.fieldID = insertCustomField(siteResourceID=arguments.csrid, usageID=arguments.usageID, detailID=arguments.detailID, fieldTypeCode=arguments.fldTypeCode, 
											fieldText=arguments.fldText, fieldReference=arguments.fldRef, adminOnly=arguments.adminOnly, dispOnly=arguments.dispOnly, 
											autoFillReg=arguments.autoFillReg, isRequired=arguments.isReq, requiredMsg=arguments.reqMsg, inventory=arguments.inventory, 
											amount=arguments.amt, GLAccountID=arguments.glID, ftdImgConfigID=arguments.ftdImgConfigID, parentUsageID=arguments.parentUsageID, 
											fieldGroupingID=local.fieldGroupingID);
			local.returnStruct.thisaction = "insert";
		}

		if (arguments.enableConditions)
			insertFieldConditions(fieldID=local.returnStruct.fieldID, strCondition=arguments.strCondition);

		local.returnStruct.success = true;
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addFieldGrouping" access="private" output="false" returntype="numeric">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="usageID" type="numeric" required="yes">
		<cfargument name="detailID" type="numeric" required="yes">
		<cfargument name="fieldGroupingName" type="string" required="yes">
		<cfargument name="fieldGroupingDesc" type="string" required="yes">

		<cfset var fieldGroupingID = 0>

		<cfstoredproc procedure="cf_addFieldGrouping" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">
			<cfif arguments.detailID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldGroupingName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldGroupingDesc#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="fieldGroupingID">
		</cfstoredproc>

		<cfreturn fieldGroupingID>
	</cffunction>

	<cffunction name="insertCustomField" access="private" output="false" returntype="numeric">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="usageID" type="numeric" required="yes">
		<cfargument name="detailID" type="numeric" required="yes">
		<cfargument name="fieldTypeCode" type="string" required="yes">
		<cfargument name="fieldText" type="string" required="yes">
		<cfargument name="fieldReference" type="string" required="yes">
		<cfargument name="adminOnly" type="boolean" required="yes">
		<cfargument name="dispOnly" type="boolean" required="yes">
		<cfargument name="autoFillReg" type="boolean" required="yes">
		<cfargument name="isRequired" type="boolean" required="yes">
		<cfargument name="requiredMsg" type="string" required="yes">
		<cfargument name="inventory" type="string" required="true">
		<cfargument name="amount" type="string" required="true">
		<cfargument name="GLAccountID" type="string" required="true">
		<cfargument name="ftdImgConfigID" type="string" required="true">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="fieldGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfif arguments.parentUsageID is 1 and ListFindNoCase("INTEGERTEXTBOXQTY,INTEGERTEXTBOXQTYAMT",arguments.fieldTypeCode)>
			<cfset local.arrPriceTypes = arrayNew(1)>
			<cfset local.arrScheduleAmt = listToArray(arguments.amount,'|')>
			<cfloop array="#local.arrScheduleAmt#" index="local.thisScheduleAmt">
				<cfset local.strTemp = { scheduleID=getToken(local.thisScheduleAmt,1,"_"), amount=getToken(local.thisScheduleAmt,2,"_") }>
				<cfset arrayAppend(local.arrPriceTypes,local.strTemp)>
			</cfloop>
			<cfset arguments.amount = 0>
		</cfif>

		<cfstoredproc procedure="cf_addField" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">
			<cfif arguments.detailID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldTypeCode#">
			<cfif arguments.fieldGroupingID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.fieldText#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldReference#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isRequired#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.requiredMsg#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.adminOnly#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.dispOnly#">
			<cfif arguments.fieldTypeCode is 'NAMETEXTBOX'>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.autoFillReg#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.amount)#">
			<cfif val(arguments.GLAccountID) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif arguments.fieldTypeCode EQ 'FEATUREDIMAGE' AND val(arguments.ftdImgConfigID) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ftdImgConfigID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif val(arguments.inventory) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.fieldID">
		</cfstoredproc>

		<cfif local.fieldID gt 0 and arguments.parentUsageID is 1 and ListFindNoCase("INTEGERTEXTBOXQTY,INTEGERTEXTBOXQTYAMT",arguments.fieldTypeCode) and arrayLen(local.arrPriceTypes)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertCustomPriceDetails">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					BEGIN TRAN;
						<cfloop array="#local.arrPriceTypes#" index="local.thisType">
							INSERT INTO dbo.cf_fieldAvailable_usage1 (fieldID, scheduleID, amount)
							VALUES (
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fieldID#">,
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisType.scheduleID#">,
								<cfqueryparam cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(replace(local.thisType.amount,',','','ALL'),"0.00")#">
								);
						</cfloop>
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cfreturn local.fieldID>
	</cffunction>

	<cffunction name="updateCustomField" access="private" output="false" returntype="void">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="usageID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="yes">
		<cfargument name="fieldTypeCode" type="string" required="yes">
		<cfargument name="fieldText" type="string" required="yes">
		<cfargument name="fieldReference" type="string" required="yes">
		<cfargument name="adminOnly" type="boolean" required="yes">
		<cfargument name="dispOnly" type="boolean" required="yes">
		<cfargument name="autoFillReg" type="boolean" required="yes">
		<cfargument name="isRequired" type="boolean" required="yes">
		<cfargument name="requiredMsg" type="string" required="yes">
		<cfargument name="inventory" type="string" required="true">
		<cfargument name="amount" type="string" required="true">
		<cfargument name="GLAccountID" type="string" required="true">
		<cfargument name="ftdImgConfigID" type="string" required="true">
		<cfargument name="fieldUID" type="string" required="yes">
		<cfargument name="parentUsageID" type="numeric" required="true">
		<cfargument name="fieldGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfif arguments.parentUsageID is 1 and ListFindNoCase("INTEGERTEXTBOXQTY,INTEGERTEXTBOXQTYAMT",arguments.fieldTypeCode)>
			<cfset local.arrPriceTypes = arrayNew(1)>
			<cfset local.arrScheduleAmt = listToArray(arguments.amount,'|')>
			<cfloop array="#local.arrScheduleAmt#" index="local.thisScheduleAmt">
				<cfset local.strTemp = { scheduleID=getToken(local.thisScheduleAmt,1,"_"), amount=getToken(local.thisScheduleAmt,2,"_") }>
				<cfset arrayAppend(local.arrPriceTypes,local.strTemp)>
			</cfloop>
			<cfset arguments.amount = 0>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteResourceID int, @fieldID int, @fieldTypeCode varchar(40), @fieldReference varchar(128), 
					@usageID int, @fieldGroupingUsageID int, @detailID int, @currentFieldGroupingID int, @fieldGroupingID int, 
					@oldFeatureImageConfigID int, @featureImageConfigID int;
				set @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
				set @fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">;
				set @fieldReference = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldReference#">;
				set @usageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.usageID#">;
				set @fieldGroupingUsageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.parentUsageID GT 0 ? arguments.parentUsageID : arguments.usageID#">;
				set @detailID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">;
				set @fieldTypeCode  = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldTypeCode#">;
				set @fieldGroupingID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">,0);

				SELECT @currentFieldGroupingID = fieldGroupingID
				FROM dbo.cf_fields
				WHERE fieldID = @fieldID;
				
				IF @fieldTypeCode <> 'LABEL' AND
					EXISTS ( select 1 
							from dbo.cf_fields 
							where controllingSiteResourceID = @siteResourceID
							and fieldReference = @fieldReference
							and usageID = @usageID
							and isnull(detailID,0) = isnull(@detailID,0)
							and isActive = 1 
							and fieldID <> @fieldID)
					RAISERROR('That export label is already in use.',16,1);

				<cfif arguments.fieldTypeCode EQ 'FEATUREDIMAGE'>
					SET @featureImageConfigID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ftdImgConfigID#">,0);

					SELECT @oldFeatureImageConfigID = featureImageConfigID
					FROM dbo.cf_fields
					WHERE fieldID = @fieldID;
				</cfif>
			
				BEGIN TRAN;
					UPDATE dbo.cf_fields
					SET fieldText = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldText#">,
						fieldReference = @fieldReference,
						isRequired = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRequired#">,
						<cfif arguments.isRequired is 1>
							requiredMsg = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.requiredMsg,250)#">,
						<cfelse>
							requiredMsg = null,
						</cfif>
						adminOnly = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.adminOnly#">,
						displayOnly = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.dispOnly#">,
						<cfif val(arguments.GLAccountID) gt 0>
							GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#">,
						<cfelse>
							GLAccountID = NULL, 
						</cfif>
						<cfif arguments.fieldTypeCode EQ 'FEATUREDIMAGE'>
							featureImageConfigID = @featureImageConfigID,
						</cfif>
						<cfif val(arguments.inventory) gt 0>
							inventory = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#">,
						<cfelse>
							inventory = NULL, 
						</cfif>
						amount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.amount)#">
						<cfif arguments.fieldTypeCode is 'NAMETEXTBOX'>
						, autoFillRegistrant = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.autoFillReg#">
						</cfif>
						<cfif len(arguments.fieldUID) and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						, uid = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.fieldUID#">
						</cfif>
					WHERE fieldID = @fieldID
					AND controllingSiteResourceID = @siteResourceID
					AND usageID = @usageID;

					IF ISNULL(@currentFieldGroupingID,0) <> ISNULL(@fieldGroupingID,0) BEGIN
						UPDATE dbo.cf_fields
						SET fieldGroupingID = @fieldGroupingID,
							fieldOrder = 9999
						WHERE fieldID = @fieldID;

						EXEC dbo.cf_reorderFields @controllingSiteResourceID=@siteResourceID, @usageID=@fieldGroupingUsageID,
							@detailID=@detailID, @fieldGroupingID=@fieldGroupingID;

						EXEC dbo.cf_reorderFields @controllingSiteResourceID=@siteResourceID, @usageID=@fieldGroupingUsageID,
							@detailID=@detailID, @fieldGroupingID=@currentFieldGroupingID;
					END

					<cfif arguments.parentUsageID is 1 and ListFindNoCase("INTEGERTEXTBOXQTY,INTEGERTEXTBOXQTYAMT",arguments.fieldTypeCode)>
						DELETE FROM dbo.cf_fieldAvailable_usage1
						WHERE fieldID = @fieldID;

						<cfif structKeyExists(local,"arrPriceTypes") and arrayLen(local.arrPriceTypes)>
							<cfloop array="#local.arrPriceTypes#" index="local.thisType">
								INSERT INTO dbo.cf_fieldAvailable_usage1 (fieldID, scheduleID, amount)
								VALUES (
									@fieldID,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisType.scheduleID#">,
									<cfqueryparam cfsqltype="CF_SQL_DOUBLE" value="#NumberFormat(replace(local.thisType.amount,',','','ALL'),"0.00")#">
									);
							</cfloop>
						</cfif>
					</cfif>
				COMMIT TRAN;

				SELECT 1 AS success
					<cfif arguments.fieldTypeCode EQ 'FEATUREDIMAGE'>
						, CASE WHEN @oldFeatureImageConfigID <> @featureImageConfigID THEN 1 ELSE 0 END AS hasFeaturedImageConfigChange
					</cfif>;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif local.qryUpdate.success AND arguments.fieldTypeCode EQ 'FEATUREDIMAGE' AND local.qryUpdate.hasFeaturedImageConfigChange>
			<cfset createObject("component","model.admin.common.modules.featuredImages.featuredImages").saveFeaturedImageConfigSettings(
					featureImageConfigID=arguments.ftdImgConfigID, referenceID=arguments.fieldID, referenceType="customField", 
					arrFeaturedImageSizes=[])>
		</cfif>
	</cffunction>

	<cffunction name="deleteCustomField" access="public" output="false" returntype="struct">
		<cfargument name="fieldID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "errmsg": "" }>

		<cftry>
			<cfstoredproc procedure="cf_deleteField" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Cannot Delete a Monetary Field Tied to a Transaction", cfcatch.detail)>
				<cfset local.returnStruct.errmsg = "Cannot Delete a Monetary Field Tied to a Transaction.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteFieldValue" access="public" output="false" returntype="struct">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "errmsg": "" }>

		<cftry>
			<cfstoredproc procedure="cf_deleteFieldValue" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Cannot Delete a Monetary Field Value Tied to a Transaction", cfcatch.detail)>
				<cfset local.returnStruct.errmsg = "Cannot Delete a Monetary Field Value Tied to a Transaction.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteResourceFields" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="resourceType" type="string" required="yes">
		<cfargument name="areaName" type="string" required="yes">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cf_deleteResourceFields" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.resourceType#"> 
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.areaName#">
			<cfif arguments.csrid gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif arguments.detailID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
		</cfstoredproc>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteFieldDocument" access="public" output="false" returntype="struct">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cf_deleteDocument" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
		</cfstoredproc>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteFieldGrouping" access="public" output="false" returntype="struct">
		<cfargument name="fieldGroupingID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteEvDocGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @fieldGroupingID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">,
						@controllingSiteResourceID int, @usageID int, @detailID int;

					SELECT @controllingSiteResourceID = fieldControllingSiteResourceID, @detailID = fieldDetailID, @usageID = fieldUsageID
					FROM dbo.cf_fieldsGrouping
					WHERE fieldGroupingID = @fieldGroupingID;
				
					BEGIN TRAN;
						UPDATE dbo.cf_fields
						SET fieldGroupingID = NULL,
							fieldOrder = 999
						WHERE fieldGroupingID = @fieldGroupingID;

						DELETE FROM dbo.cf_fieldsGrouping
						WHERE fieldGroupingID = @fieldGroupingID;

						EXEC dbo.cf_reorderFieldGroupings @fieldControllingSiteResourceID=@controllingSiteResourceID, @fieldUsageID=@usageID, @fieldDetailID=@detailID;

						EXEC dbo.cf_reorderFields @controllingSiteResourceID=@controllingSiteResourceID, @usageID=@usageID, 
							@detailID=@detailID, @fieldGroupingID=NULL;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>	
		</cfcatch>
		</cftry>	

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doMoveFieldGrouping" access="public" output="false" returntype="struct" hint="Re-Order Custom-Field Grouping">
		<cfargument name="fieldGroupingID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cf_moveFieldGrouping">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldGroupingID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doMoveCustomField" access="public" output="false" returntype="struct" hint="Re-Order Custom-Field">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cf_moveField">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>


	<cffunction name="doMoveFieldValue" access="public" output="false" returntype="struct" hint="Re-Order Custom-Field-Option">
		<cfargument name="valueID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cf_moveFieldValue">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFields" access="public" output="no" returntype="query">
		<cfargument name="usageRT" type="string" required="true">
		<cfargument name="usageAN" type="string" required="true">
		<cfargument name="csrID" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">

		<cfset var qryCustomFields = "">

		<cfquery name="qryCustomFields" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageID int, @parentUsageID int, @controllingSiteResourceID int, @detailID int;
			SELECT @usageID = dbo.fn_cf_getUsageID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageRT#">,<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.usageAN#">, NULL);

			SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrID#">;
			SET @detailID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">,0);

			SELECT @parentUsageID = CASE WHEN parentUsageID IS NOT NULL THEN parentUsageID ELSE usageID END
			FROM dbo.cf_fieldUsages 
			WHERE usageID = @usageID;

			WITH FieldGroupings AS (
				SELECT fieldGroupingID, fieldGroupingOrder, fieldGrouping
				FROM dbo.cf_fieldsGrouping
				WHERE fieldControllingSiteResourceID = @controllingSiteResourceID
				AND fieldUsageID = @parentUsageID
				AND ISNULL(fieldDetailID,0) = ISNULL(@detailID,0)
					UNION ALL
				SELECT 0, 0, 'Default - No Grouping'
			)
			select fg.fieldGroupingID, fg.fieldGrouping, fg.fieldGroupingOrder, f.fieldID, f.fieldTypeID, f.usageID, f.adminOnly, 
				f.inventory, ft.displayTypeCode, ft.dataTypeCode, ft.supportQty, 
				case when fu.parentUsageID is not null then f.fieldText + ' ' + quoteName(fu.areaName) else f.fieldText end as fieldText, 
				case 
				when ft.supportQty = 1 then 
					(select isnull(sum(fv.valueInteger),0)
					from dbo.cf_fieldData as fd
					inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					where fd.fieldID = f.fieldID)
				else null 
				end as inventoryCount, f.fieldOrder
			from FieldGroupings as fg
			left outer join dbo.cf_fields as f 
				inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					and f.controllingSiteResourceID = @controllingSiteResourceID
					<cfif arguments.detailID gt 0>
						and f.detailID = @detailID
					</cfif>
					and f.isActive = 1
				inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
					and 1 = case when fu.parentUsageID is not null and fu.parentUsageID = @usageID then 1
								 when fu.parentUsageID is null and f.usageID = @usageID then 1
								 else 0 end
				on fg.fieldGroupingID = isnull(f.fieldGroupingID,0)
			order by fg.fieldGroupingOrder, f.fieldOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCustomFields>
	</cffunction>

	<cffunction name="getFieldsXML" access="public" output="no" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="resourceType" type="string" required="yes">
		<cfargument name="areaName" type="string" required="yes">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="yes">
		<cfargument name="hideAdminOnly" type="boolean" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cf_getFieldsXML" datasource="#application.dsn.membercentral.dsn#" cachedWithin="request">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.resourceType#"> 
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.areaName#">
			<cfif arguments.csrid gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.csrid#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfif arguments.detailID gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.detailID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.hideAdminOnly#">
			<cfprocresult name="local.qryFieldsXML">
		</cfstoredproc>

		<cfreturn local.qryFieldsXML>
	</cffunction>

	<cffunction name="getResponses" access="public" output="false" returntype="query">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="restrictToFieldID" type="numeric" required="false" default="0">

		<cfset var qryResponseDetails = "">

		<cfstoredproc procedure="cf_getResponses" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			<cfif arguments.restrictToFieldID GT 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.restrictToFieldID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfprocresult name="qryResponseDetails" resultset="1">
		</cfstoredproc>

		<cfreturn qryResponseDetails>
	</cffunction>

	<cffunction name="getFieldResponseEntered" access="public" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">

		<cfset var qryFieldDetails = getResponses(itemType=arguments.itemType, itemID=arguments.itemID, restrictToFieldID=arguments.fieldID)>
		<cfreturn qryFieldDetails.customValue>
	</cffunction>

	<cffunction name="getFieldOptions" access="public" output="false" returntype="query">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="restrictToValueIDList" type="string" required="false" default="">

		<cfset var qryFieldOptions = "">

		<cfstoredproc procedure="cf_getFieldOptions" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			<cfif len(arguments.restrictToValueIDList)>
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.restrictToValueIDList#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" null="true">
			</cfif>
			<cfprocresult name="qryFieldOptions" resultset="1">
		</cfstoredproc>

		<cfreturn qryFieldOptions>
	</cffunction>

	<cffunction name="getFieldOptionsSelected" access="public" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">

		<cfset var qryDetails = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDetails">
			SELECT fd.valueID
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID and fv.fieldID = f.fieldID
			WHERE fd.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			AND fd.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			AND fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
		</cfquery>		

		<cfreturn valueList(qryDetails.valueID)>
	</cffunction>

	<cffunction name="getFieldOptionActualFee" access="public" output="false" returntype="numeric">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="valueID" type="numeric" required="yes">
		<cfargument name="applicationType" type="string" required="yes">
		<cfargument name="trItemType" type="string" required="yes">

		<cfset var qryDetails = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDetails">
			set nocount on;

			declare @fddataid int, @applicationTypeID int;
			select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.applicationType#">);

			SELECT @fddataid = fd.dataID
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID and fv.fieldID = f.fieldID
			WHERE fd.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			AND fd.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			AND fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			and fv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueID#">;

			SELECT sum(tsFull.cache_amountAfterAdjustment) as amount
			FROM dbo.tr_applications as a
			inner join dbo.tr_transactions as t on t.transactionID = a.transactionID
			CROSS APPLY dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
			WHERE a.applicationTypeID = @applicationTypeID
			and a.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.trItemType#">
			and a.itemID = @fddataid
			and a.status = 'A';
		</cfquery>

		<cfreturn val(qryDetails.amount)>
	</cffunction>

	<cffunction name="getFieldActualFee" access="public" output="false" returntype="numeric">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="applicationType" type="string" required="yes">
		<cfargument name="trItemType" type="string" required="yes">

		<cfset var qryDetails = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDetails">
			set nocount on;

			declare @applicationTypeID int;
			select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.applicationType#">);

			SELECT sum(tsFull.cache_amountAfterAdjustment) as amount
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.tr_applications as a on a.applicationTypeID = @applicationTypeID
				and a.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.trItemType#">
				and a.itemID = fd.dataID
				and a.status = 'A'
			inner join dbo.tr_transactions as t on t.transactionID = a.transactionID
			CROSS APPLY dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
			WHERE fd.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			AND fd.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			AND fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;
		</cfquery>

		<cfreturn val(qryDetails.amount)>
	</cffunction>

	<cffunction name="getFieldQTYDetails" access="public" output="false" returntype="query">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">
		<cfargument name="applicationType" type="string" required="yes">
		<cfargument name="trItemType" type="string" required="yes">

		<cfset var qryDetails = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDetails">
			set nocount on;

			declare @applicationTypeID int;
			select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.applicationType#">);

			SELECT a.subItemID, t.detail, sum(tsFull.cache_amountAfterAdjustment) as amount
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.tr_applications as a on a.applicationTypeID = @applicationTypeID
				and a.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.trItemType#">
				and a.itemID = fd.dataID
				and a.status = 'A'
			INNER JOIN dbo.tr_transactions as t on t.transactionID = a.transactionID
			CROSS APPLY dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
			WHERE fd.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			AND fd.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			AND fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			GROUP BY a.subItemID, t.detail
			ORDER BY a.subItemID;
		</cfquery>

		<cfreturn qryDetails>
	</cffunction>

	<cffunction name="getFieldDocumentDetails" access="public" output="false" returntype="struct">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="fieldID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFieldDocDetails">
			SELECT fv.valueSiteResourceID, isnull(dv.filename,'') as fileName
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID and fv.fieldID = f.fieldID
			INNER JOIN dbo.cms_documents as d on d.siteResourceID = fv.valueSiteResourceID
			INNER JOIN dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
			INNER JOIN dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
			WHERE fd.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
			AND fd.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			AND fd.fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['siteResourceID'] = local.qryFieldDocDetails.valueSiteResourceID>
		<cfset local.returnStruct['fileName'] = local.qryFieldDocDetails.fileName>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="renderResourceFields" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="viewMode" type="string" required="yes" hint="default or responsive or bs4">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="areaName" type="string" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="yes">
		<cfargument name="hideAdminOnly" type="boolean" required="yes">
		<cfargument name="itemType" type="string" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="trItemType" type="string" required="yes">
		<cfargument name="trApplicationType" type="string" required="yes">
		<cfargument name="strFieldValues" type="struct" required="no" default="#structNew()#">
		<cfargument name="excludeNonRequiredFields" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.strReturn = { hasFields="", jsvalidation="", head="", html="" }>

		<cfset local.qryFieldsXML = getFieldsXML(siteID=arguments.siteID, resourceType=arguments.resourceType, areaName=arguments.areaName, csrid=arguments.csrid, detailID=arguments.detailID, hideAdminOnly=arguments.hideAdminOnly)>
		<cfset local.fieldsXML = xmlParse(local.qryFieldsXML.returnXML).xmlRoot>

		<cfset local.strReturn.hasFields = arrayLen(local.fieldsXML.xmlChildren) gt 0>

		<cfif not local.strReturn.hasFields>
			<cfreturn local.strReturn>
		</cfif>

		<cfscript>
			// default grouping fields
			local.arrGroupingFields = [{
				"fieldGroupingID":0,
				"fieldGrouping":'',
				"fieldGroupingDesc":'',
				"arrFields":xmlSearch(local.fieldsXML,"//fields/field[@fieldGroupingID='0']")
			}];

			// grouping fields
			local.qryFieldGroupings = getFieldUsageFieldGroupings(usageRT=arguments.resourceType, usageAN=arguments.areaName, csrid=arguments.csrid, detailID=arguments.detailID);
			
			for (local.strFieldGrouping in local.qryFieldGroupings) {
				local.arrGroupingFields.append({
					"fieldGroupingID":local.strFieldGrouping.fieldGroupingID,
					"fieldGrouping":local.strFieldGrouping.fieldGrouping,
					"fieldGroupingDesc":local.strFieldGrouping.fieldGroupingDesc,
					"arrFields":xmlSearch(local.fieldsXML,"//fields/field[@fieldGroupingID='#local.strFieldGrouping.fieldGroupingID#']")
				});
			}

			if (arguments.excludeNonRequiredFields){
				local.filteredFields = arrayFilter(local.fieldsXML.xmlChildren, function(item){
					return item.xmlattributes.IsRequired is 1;
				});
				local.fieldsXML.xmlChildren = local.filteredFields;	
			}
		</cfscript>

		<cfif arrayLen(xmlSearch(local.fieldsXML,"//fields/field[@dataTypeCode='DOCUMENTOBJ']"))>
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		</cfif>

		<!--- show currency types --->
		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = " #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultCurrencyType#">
		</cfif>

		<cfset local.fldContainerID = "mccf_div_#timeFormat(now(),'hhmmssl')#">

		<cfsavecontent variable="local.strReturn.jsvalidation">
			<cfoutput>
			/*required fields*/
			var mccf_fldsRequired = $('###local.fldContainerID#').find('input:text[data-IsRequired="1"], input:file[data-IsRequired="1"], select[data-IsRequired="1"], textarea[data-IsRequired="1"]').not(':disabled').not(':hidden');

			/*distinct radio, checkbox elements*/
			var radioCheckBoxElements = $('###local.fldContainerID#').find('input:radio[data-IsRequired="1"], input:checkbox[data-IsRequired="1"]').not(':hidden');
			
			var elemArr = [];
			$.each( radioCheckBoxElements, function() {
				var elemName = this.name;
				if( $.inArray( elemName, elemArr ) < 0 ){
					elemArr.push(elemName);
					mccf_fldsRequired.push(this);
				}
			});

			var mccf_fldsRequiredErrorMsgArray = $.map(mccf_fldsRequired,mccf_validate_fieldIsRequired);
			Array.prototype.push.apply(errorMsgArray, mccf_fldsRequiredErrorMsgArray);

			var integerTextControls = $('###local.fldContainerID#').find('input[data-displayTypeCode="TEXTBOX"][data-dataTypeCode="INTEGER"]').not(':disabled').not(':hidden');
			var integerTextControlsErrorMsgArray = $.map(integerTextControls,mccf_validate_textControlValidInteger);
			Array.prototype.push.apply(errorMsgArray, integerTextControlsErrorMsgArray);

			var decimalTextControls = $('###local.fldContainerID#').find('input[data-displayTypeCode="TEXTBOX"][data-dataTypeCode="DECIMAL2"]').not(':disabled').not(':hidden');
			var decimalTextControlsErrorMsgArray = $.map(decimalTextControls,mccf_validate_textControlValidDecimal);
			Array.prototype.push.apply(errorMsgArray, decimalTextControlsErrorMsgArray);
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.head">
			<cfoutput>
			<script type="text/javascript">
				$(function() { 
					mccf_setupDateControls($('###local.fldContainerID#'), 'MCCFDateControl', 'MCCFDateControlClearLink');
					<cfif arrayLen(xmlSearch(local.fieldsXML,"//fields/field[@fieldTypeCode='NAMETEXTBOX']"))>
						mccf_loadNameControl($('###local.fldContainerID#'));
					</cfif>
				});
			</script>
			<cfif arguments.viewMode neq 'bs4'>
				<style>
				###local.fldContainerID# input.MCCFDateControl {background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
				</style>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.html">
			<cfswitch expression="#arguments.viewMode#">
				<cfcase value="default">
					<cfinclude template="cf_renderDefaultView.cfm">
				</cfcase>
				<cfcase value="responsive">
					<cfinclude template="cf_renderResponsiveView.cfm">
				</cfcase>
				<cfcase value="bs4">
					<cfinclude template="cf_renderBS4View.cfm">
				</cfcase>
				<cfcase value="feEvReg,feSWReg" delimiters=",">
					<cfset local.prependClassName = arguments.viewMode EQ 'feEvReg' ? "evreg" : "swreg">
					<cfinclude template="cf_renderFERegFields.cfm">
				</cfcase>
				<cfdefaultcase>
					<cfinclude template="cf_renderDefaultView.cfm">
				</cfdefaultcase>
			</cfswitch>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getResourceFieldsArrayFromFieldsXML" access="public" output="false" returntype="array">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="usageRT" type="string" required="true">
		<cfargument name="usageAN" type="string" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="detailID" type="numeric" required="true">
		<cfargument name="fieldsXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.JSONInsanityChars = "^~~~^">
		
		<cfscript>
			// default grouping fields
			local.arrResourceFields = [{
				"fieldGroupingID":0,
				"fieldGrouping":'',
				"fieldGroupingDesc":'',
				"arrFields":[]
			}];
			local.strFGFields = {
				"0": xmlSearch(arguments.fieldsXML,"//fields/field[@fieldGroupingID='0']")
			};

			// grouping fields
			local.qryFieldGroupings = getFieldUsageFieldGroupings(usageRT=arguments.usageRT, usageAN=arguments.usageAN, csrid=arguments.csrid, detailID=arguments.detailID);
			
			for (local.strFieldGrouping in local.qryFieldGroupings) {
				local.strFGFields[local.strFieldGrouping.fieldGroupingID] = xmlSearch(arguments.fieldsXML,"//fields/field[@fieldGroupingID='#local.strFieldGrouping.fieldGroupingID#']");

				local.arrResourceFields.append({
					"fieldGroupingID":local.strFieldGrouping.fieldGroupingID,
					"fieldGrouping":local.strFieldGrouping.fieldGrouping,
					"fieldGroupingDesc":local.strFieldGrouping.fieldGroupingDesc,
					"arrFields":[]
				});
			}
		</cfscript>
		
		<cfloop array="#local.arrResourceFields#" index="local.thisGrouping">
			<cfloop array="#local.strFGFields[local.thisGrouping.fieldGroupingID]#" index="local.thisfield">
				<cfset local.strThisField = duplicate(local.thisfield)>
				<cfset local.strThisFieldXMLAttributes = duplicate(local.strThisField.xmlattributes)>

				<cfset local.tmpFieldsStr = structNew()>
				<cfset local.tmpFieldsStr['itemID'] = arguments.itemID>
				<cfset local.tmpFieldsStr['fieldID'] = local.strThisFieldXMLAttributes.fieldID>
				<cfset local.tmpFieldsStr['attributes'] = duplicate(local.strThisFieldXMLAttributes)>
				<cfset local.tmpFieldsStr['attributes']['fieldText'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldText']>
				<cfset local.tmpFieldsStr['attributes']['fieldReference'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldReference']>
				<cfset local.tmpFieldsStr['dataTypeCode'] = local.strThisFieldXMLAttributes.dataTypeCode>
				<cfset local.tmpFieldsStr['displayTypeCode'] = local.strThisFieldXMLAttributes.displayTypeCode>
				<cfset local.tmpFieldsStr['fieldTypeCode'] = local.strThisFieldXMLAttributes.fieldTypeCode>
				<cfset local.tmpFieldsStr['supportAmt'] = val(local.strThisFieldXMLAttributes.supportAmt)>
				<cfset local.tmpFieldsStr['supportQty'] = val(local.strThisFieldXMLAttributes.supportQty)>
				<cfset local.tmpFieldsStr['isRequired'] = val(local.strThisFieldXMLAttributes.isRequired)>
				<cfset local.tmpFieldsStr['requiredMsg'] = local.strThisFieldXMLAttributes.requiredMsg>
				<cfset local.tmpFieldsStr['children'] = arrayNew(1)>
				<cfset local.tmpFieldsStr['allOptionEmptyOrDisabled'] = 0>
				<cfset local.tmpFieldsStr['value'] = "">

				<cfif arguments.itemID gt 0>
					<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
						<cfset local.tmpFieldsStr['value'] = getFieldOptionsSelected(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
					<cfelse>
						<cfset local.tmpFieldsStr['value'] = getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
						<cfif local.strThisFieldXMLAttributes.displayTypeCode is 'TEXTBOX' and local.strThisFieldXMLAttributes.supportQty is 1>
							<cfset local.tmpFieldsStr['value'] = val(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				</cfif>

				<cfif local.strThisFieldXMLAttributes.displayTypeCode eq 'TEXTBOX' and local.strThisFieldXMLAttributes.supportQty is 1>
					<cfset local.maxQtyAllowed = 99999>
					<cfif local.strThisFieldXMLAttributes.fieldInventory gt 0>
						<cfif local.strThisFieldXMLAttributes.fieldInventory lte local.strThisFieldXMLAttributes.fieldinventoryCount>
							<cfset local.maxQtyAllowed = 0>
							<cfset local.tmpFieldsStr['isRequired'] = 0>
						<cfelse>
							<cfset local.maxQtyAllowed = local.strThisFieldXMLAttributes.fieldInventory-local.strThisFieldXMLAttributes.fieldinventoryCount>
							<!--- edit case of non monetary qty-field --->
							<cfif local.strThisFieldXMLAttributes.supportAmt is 0 and val(local.tmpFieldsStr['value']) gt 0>
								<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpFieldsStr['value'])>
							</cfif>
						</cfif>
					</cfif>
					<cfset local.tmpFieldsStr['maxQtyAllowed'] = local.maxQtyAllowed>
				</cfif>

				<!--- dont show question at all if select,radio,checkbox and no options defined --->
				<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
					<cfif arrayLen(local.strThisField.xmlchildren) is 0>
						<cfset local.tmpFieldsStr.allOptionEmptyOrDisabled = 1>
					<cfelse>
						<cfloop array="#local.strThisField.xmlchildren#" index="local.thisoption">
							<cfset local.strThisOption = duplicate(local.thisoption)>
							<cfset local.strThisOptionXMLAttributes = duplicate(local.strThisOption.xmlattributes)>

							<cfset local.tmpOptionStr = structNew()>
							<cfset local.tmpOptionStr['attributes'] = duplicate(local.strThisOptionXMLAttributes)>
							<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.JSONInsanityChars & local.tmpOptionStr['attributes']['fieldValue']>
							<cfset local.tmpOptionStr['unavailable'] = 0>
							
							<!--- skip unavailability check for an already selected option --->
							<cfif len(local.tmpFieldsStr['value']) and listFind(local.tmpFieldsStr['value'],local.strThisOptionXMLAttributes.valueID)>
							
							<cfelseif local.strThisOptionXMLAttributes.optionInventory gt 0 and local.strThisOptionXMLAttributes.optionInventory lte local.strThisOptionXMLAttributes.optioninventoryCount>
								<cfset local.tmpOptionStr.unavailable = 1>
							</cfif>
							<cfset arrayAppend(local.tmpFieldsStr.children, local.tmpOptionStr)>
						</cfloop>

						<cfif local.tmpFieldsStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpFieldsStr['value'])>
							<cfset local.tmpFieldsStr['value'] = listToArray(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				<!--- append json insanity vars for other display type values --->
				<cfelseif len(local.tmpFieldsStr['value'])>
					<cfset local.tmpFieldsStr['value'] = local.JSONInsanityChars & local.tmpFieldsStr['value']>
				</cfif>
		
				<cfset arrayAppend(local.thisGrouping.arrFields, local.tmpFieldsStr)>
			</cfloop>
		</cfloop>
		
		<cfreturn local.arrResourceFields>
	</cffunction>

	<cffunction name="generateFieldFilterSQL" access="public" output="false" returntype="struct">
		<cfargument name="rc" type="struct" required="true">
		<cfargument name="fieldIDPrefix" type="string" required="true">
		<cfargument name="fieldExpPrefix" type="string" required="true">
		<cfargument name="strItemIDSQLVariable" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { filterSQL='', filterSetupSQL='' }>

		<cfset local.strFields = {}>
		<cfloop collection="#arguments.rc#" item="local.thisFormField">
			<cfif left(local.thisFormField,len(arguments.fieldIDPrefix)) eq arguments.fieldIDPrefix and len(arguments.rc[local.thisFormField])>
				<cfset local.arrCFID = listToArray(arguments.rc[local.thisFormField],'_')>
				<cfif isValid("integer",local.arrCFID[1]) and structKeyExists(arguments.rc,"#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp") and len(arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp'])>
					<cfif listFindNoCase("exists,not_exists",arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp'])>
						<cfset local.tmpStr = { exp=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp'], displaycode=local.arrCFID[2], datacode=local.arrCFID[3], itemType=local.arrCFID[4] }>
						<cfset structInsert(local.strFields, local.arrCFID[1], local.tmpStr)>					
					<cfelse>
						<cfif structKeyExists(arguments.rc,"#arguments.fieldExpPrefix#_#local.arrCFID[1]#") and len(arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#'])>
							<cfset local.tmpStr = { exp=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp'], value=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#'], displaycode=local.arrCFID[2], datacode=local.arrCFID[3], itemType=local.arrCFID[4] }>
							<cfset structInsert(local.strFields, local.arrCFID[1], local.tmpStr)>
						<cfelseif structKeyExists(arguments.rc,"#arguments.fieldExpPrefix#_#local.arrCFID[1]#_lower") and len(arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_lower'])>
							<cfset local.tmpStr = { exp=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_exp'], valuelower=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_lower'], valueupper=arguments.rc['#arguments.fieldExpPrefix#_#local.arrCFID[1]#_upper'], displaycode=local.arrCFID[2], datacode=local.arrCFID[3], itemType=local.arrCFID[4] }>
							<cfset structInsert(local.strFields, local.arrCFID[1], local.tmpStr)>					
						</cfif>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>

		<cfloop collection="#local.strFields#" item="local.thisField">
			<cfsavecontent variable="local.strReturn.filterSetupSQL">
				<cfoutput>
				#local.strReturn.filterSetupSQL#
				declare @fieldID#local.thisField# int = #local.thisField#;
				</cfoutput>
			</cfsavecontent>
			<cfswitch expression="#local.strFields[local.thisField].exp#">
				<cfcase value="exists">
					<cfsavecontent variable="local.strReturn.filterSQL">
						<cfoutput>
						#local.strReturn.filterSQL#
						AND EXISTS (
							select 1 
							from dbo.cf_fieldData 
							where fieldID = @fieldID#local.thisField#
							and itemType = '#local.strFields[local.thisField].itemType#' 
							and itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
						)
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfcase value="not_exists">
					<cfsavecontent variable="local.strReturn.filterSQL">
						<cfoutput>
						#local.strReturn.filterSQL#
						AND NOT EXISTS (
							select 1 
							from dbo.cf_fieldData 
							where fieldID = @fieldID#local.thisField#
							and itemType = '#local.strFields[local.thisField].itemType#' 
							and itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
						)
						</cfoutput>
					</cfsavecontent>
				</cfcase>
				<cfdefaultcase>
					<cfswitch expression="#local.strFields[local.thisField].displayCode#">
						<cfcase value="TEXTBOX">
							<cfsavecontent variable="local.strReturn.filterSetupSQL">
								<cfoutput>
								#local.strReturn.filterSetupSQL#
								<cfswitch expression="#local.strFields[local.thisField].dataCode#">
									<cfcase value="STRING">
										declare @fieldID#local.thisField#val varchar(max) = '#replace(local.strFields[local.thisField].value,"'","''","ALL")#';
									</cfcase>
									<cfcase value="DECIMAL2">
										declare @fieldID#local.thisField#val decimal(14,2) = #replace(local.strFields[local.thisField].value,"'","''","ALL")#;
									</cfcase>
									<cfcase value="INTEGER">
										declare @fieldID#local.thisField#val int = #replace(local.strFields[local.thisField].value,"'","''","ALL")#;
									</cfcase>
								</cfswitch>
								</cfoutput>
							</cfsavecontent>
							<cfsavecontent variable="local.strReturn.filterSQL">
								<cfoutput>
								#local.strReturn.filterSQL#
								AND EXISTS (
									select 1
									from dbo.cf_fieldData as fd
									inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
									where fd.fieldID = @fieldID#local.thisField#
									and fd.itemType = '#local.strFields[local.thisField].itemType#'
									and fd.itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
									<cfswitch expression="#local.strFields[local.thisField].dataCode#">
										<cfcase value="STRING">
											and fv.valueString = @fieldID#local.thisField#val
										</cfcase>
										<cfcase value="DECIMAL2">
											and fv.valueDecimal2 = @fieldID#local.thisField#val
										</cfcase>
										<cfcase value="INTEGER">
											and fv.valueInteger = @fieldID#local.thisField#val
										</cfcase>
									</cfswitch>
								)
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="SELECT,CHECKBOX,RADIO">
							<cfsavecontent variable="local.strReturn.filterSetupSQL">
								<cfoutput>
								#local.strReturn.filterSetupSQL#
								declare @fieldID#local.thisField#val int = #replace(local.strFields[local.thisField].value,"'","''","ALL")#;
								</cfoutput>
							</cfsavecontent>
							<cfsavecontent variable="local.strReturn.filterSQL">
								<cfoutput>
								#local.strReturn.filterSQL#
								AND EXISTS (
									select 1
									from dbo.cf_fieldData
									where fieldID = @fieldID#local.thisField#
									and itemType = '#local.strFields[local.thisField].itemType#'
									and itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
									and valueID = @fieldID#local.thisField#val
								)						
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="TEXTAREA">
							<cfsavecontent variable="local.strReturn.filterSetupSQL">
								<cfoutput>
								#local.strReturn.filterSetupSQL#
								declare @fieldID#local.thisField#val varchar(max) = '#replace(replace(local.strFields[local.thisField].value,"'","''","ALL"),"_","\_","ALL")#';
								</cfoutput>
							</cfsavecontent>
							<cfsavecontent variable="local.strReturn.filterSQL">
								<cfoutput>
								#local.strReturn.filterSQL#
								AND EXISTS (
									select 1
									from dbo.cf_fieldData as fd
									inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
									where fd.fieldID = @fieldID#local.thisField#
									and fd.itemType = '#local.strFields[local.thisField].itemType#'
									and fd.itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
									and fv.valueString like '%'+@fieldID#local.thisField#val+'%' ESCAPE('\')
								)
								</cfoutput>
							</cfsavecontent>
						</cfcase>
						<cfcase value="DATE">
							<cfsavecontent variable="local.strReturn.filterSetupSQL">
								<cfoutput>
								#local.strReturn.filterSetupSQL#
								declare @fieldID#local.thisField#valLower date = '#replace(local.strFields[local.thisField].valueLower,"'","''","ALL")#';
								declare @fieldID#local.thisField#valUpper date = '#replace(local.strFields[local.thisField].valueUpper,"'","''","ALL")#';
								</cfoutput>
							</cfsavecontent>
							<cfsavecontent variable="local.strReturn.filterSQL">
								<cfoutput>
								#local.strReturn.filterSQL#
								AND EXISTS (
									select 1
									from dbo.cf_fieldData as fd
									inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
									where fd.fieldID = @fieldID#local.thisField#
									and fd.itemType = '#local.strFields[local.thisField].itemType#'
									and fd.itemID = #arguments.strItemIDSQLVariable[local.strFields[local.thisField].itemType]#
									and fv.valueDate between @fieldID#local.thisField#valLower and @fieldID#local.thisField#valUpper
								)
								</cfoutput>
							</cfsavecontent>
						</cfcase>
					</cfswitch>
				</cfdefaultcase>
			</cfswitch>
		</cfloop>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getBranchFieldFilterSelector" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="areaName" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="fieldLabel" type="string" required="true">
		<cfargument name="gridExt" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { qryFields="", arrFieldConditions=arrayNew(1), fieldSelectArea="", fieldSelectControls="" }>

		<cfset local.strReturn.qryFields = queryNew("fieldID,role,fieldText,displayTypeCode,dataTypeCode,fieldTypeCode,thisField","varchar,varchar,varchar,varchar,varchar,varchar,varchar")>

        <cfset local.qryEventRegStepFieldsXML = getFieldsXML(siteID=arguments.siteID, resourceType=arguments.resourceType, areaName=arguments.areaName, csrid=arguments.csrid, detailID=0, hideAdminOnly=0)>
		<cfset local.xmlBranchFields = xmlParse(local.qryEventRegStepFieldsXML.returnXML).xmlRoot>
		<cfset local.arrXMLFields = xmlSearch(local.xmlBranchFields,"//fields/field[@fieldID!='#arguments.fieldID#']")>

		<cfif arrayLen(local.arrXMLFields)>
			<cfloop array="#local.arrXMLFields#" index="local.thisfield">
				<cfif len(local.thisfield.xmlattributes.fieldReference)>
					<cfif queryAddRow(local.strReturn.qryFields)>
						<cfset querySetCell(local.strReturn.qryFields, "fieldID", local.thisfield.xmlattributes.fieldID)>
						<cfset querySetCell(local.strReturn.qryFields, "role", arguments.fieldLabel)>
						<cfset querySetCell(local.strReturn.qryFields, "fieldText", local.thisfield.xmlattributes.fieldReference)>
						<cfset querySetCell(local.strReturn.qryFields, "displayTypeCode", local.thisfield.xmlattributes.displayTypeCode)>
						<cfset querySetCell(local.strReturn.qryFields, "dataTypeCode", local.thisfield.xmlattributes.dataTypeCode)>
						<cfset querySetCell(local.strReturn.qryFields, "fieldTypeCode", local.thisfield.xmlattributes.fieldTypeCode)>
						<cfset querySetCell(local.strReturn.qryFields, "thisField", local.thisfield)>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<cfif arguments.fieldID gt 0>
			<cfquery name="local.strReturn.arrFieldConditions" datasource="#application.dsn.membercentral.dsn#" returntype="array">
				select condFieldID, condExp, isnull(condValueID,0) as condValueID
				from dbo.cf_fieldValueConditions
				where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.strReturn.fieldSelectArea">
			<cfoutput>
			<div class="row">
				<div class="col-md-12">
					<div id="divMCResourceFieldArea#arguments.gridExt#"></div>
					<div class="mt-2 ml-2">
						<a href="##" onclick="mccf_showFieldValueArea('#arguments.gridExt#');return false;"><i class="fa-solid fa-circle-plus fa-lg mr-2"></i>Add #arguments.fieldLabel# to condition</a>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.fieldSelectControls">
			<cfoutput>
			<div style="display:none;">
				<cfloop query="local.strReturn.qryFields">
					<div id="divMCCF_#local.strReturn.qryFields.fieldID#_#local.strReturn.qryFields.displayTypeCode#_#local.strReturn.qryFields.dataTypeCode#">
						<div class="row">
							<cfswitch expression="#local.strReturn.qryFields.displayTypeCode#">
								<cfcase value="SELECT,CHECKBOX,RADIO">
									<div class="col-md-3 pr-md-0">
										<cfset local.tmpXML = XMLParse(local.strReturn.qryFields.thisField)>
										<select id="mccf_#local.strReturn.qryFields.fieldID#_exp_{~n~}" name="mccf_#local.strReturn.qryFields.fieldID#_exp_{~n~}" onchange="mccf_changeExpression($(this));" class="form-control form-control-sm mccf_fieldexpression">
											<option value="eq">equals</option>
											<option value="exists">has a value</option>
											<option value="not_exists">does not have a value</option>
										</select>
									</div>
									<div class="col-md-9">
										<select id="mccf_#local.strReturn.qryFields.fieldID#_val_{~n~}" name="mccf_#local.strReturn.qryFields.fieldID#_val_{~n~}" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" onchange="mccf_validateFieldSelect($(this));" class="form-control form-control-sm">
											<option value=""></option>
											<cfloop array="#local.tmpXML.xmlRoot.xmlchildren#" index="local.thisoption">
												<option value="#local.thisoption.xmlattributes.valueID#">#local.thisoption.xmlattributes.fieldValue#</option>
											</cfloop>
										</select>
									</div>
								</cfcase>
							</cfswitch>
						</div>
					</div>
				</cfloop>

				<select name="fieldSelectControl#arguments.gridExt#" id="fieldSelectControl#arguments.gridExt#" class="form-control form-control-sm">
					<option value=""></option>
					<cfoutput query="local.strReturn.qryFields" group="role">
						<optgroup label="#local.strReturn.qryFields.role#">
						<cfoutput>
							<option value="#local.strReturn.qryFields.fieldID#_#local.strReturn.qryFields.displayTypeCode#_#local.strReturn.qryFields.dataTypeCode#">#local.strReturn.qryFields.fieldText#</option>
						</cfoutput>
						</optgroup>
					</cfoutput>
				</select>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="insertFieldConditions" access="private" output="false" returntype="void">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="strCondition" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfif len(arguments.strCondition)>
			<cfset arguments.strCondition = DeserializeJSON(arguments.strCondition)>

			<cfif isStruct(arguments.strCondition)>
				<cfset local.arrConditions = arrayNew(1)>

				<cfloop list="#structKeyList(arguments.strCondition)#" index="local.thisKey">
					<cfif left(local.thisKey,12) eq 'mccf_fieldID' and ListLen(arguments.strCondition[local.thisKey],'_') eq 3>
						<cfset local.tmpStr = { fieldID=0, exp="", valueID=0 }>
						<cfset local.tmpStr.fieldID = ListGetAt(arguments.strCondition[local.thisKey],1,'_')>
						
						<cfset local.thisRow = replace(local.thisKey,"mccf_fieldID","")>
						<cfset local.thisExpKey = "mccf_#local.tmpStr.fieldID#_exp_#local.thisRow#">
						<cfset local.thisValueIDKey = "mccf_#local.tmpStr.fieldID#_val_#local.thisRow#">

						<cfif structKeyExists(arguments.strCondition,local.thisExpKey) and listFindNoCase("eq,exists,not_exists",arguments.strCondition[local.thisExpKey])>
							<cfset local.tmpStr.exp = arguments.strCondition[local.thisExpKey]>
						</cfif>
						<cfif structKeyExists(arguments.strCondition,local.thisValueIDKey) and val(arguments.strCondition[local.thisValueIDKey]) gt 0>
							<cfset local.tmpStr.valueID = arguments.strCondition[local.thisValueIDKey]>
						</cfif>

						<cfif local.tmpStr.fieldID gt 0 and len(local.tmpStr.exp)>
							<cfset arrayAppend(local.arrConditions, local.tmpStr)>
						</cfif>
					</cfif>
				</cfloop>

				<cfif arrayLen(local.arrConditions)>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertConditions">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @fieldID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">;

							BEGIN TRAN;
								UPDATE dbo.cf_fields
								SET enableConditions = 1
								WHERE fieldID = @fieldID;
								
								DELETE FROM dbo.cf_fieldValueConditions
								WHERE fieldID = @fieldID;

								<cfloop array="#local.arrConditions#" index="local.thisCondition">
									INSERT INTO dbo.cf_fieldValueConditions (fieldID, condFieldID, condExp, condValueID)
									VALUES (@fieldID, #int(val(local.thisCondition.fieldID))#, '#local.thisCondition.exp#', NULLIF(#int(val(local.thisCondition.valueID))#,0));
								</cfloop>
							COMMIT TRAN;
						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfif>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="renderFeaturedImageField" access="public" output="false" returntype="string">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="saveImageHandler" type="string" required="true">
		<cfargument name="deleteImageHandler" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfset local.arrConfigs = 
			[{ "ftdExt":"mccf_ftdimage_#arguments.fieldID#", "controllingReferenceID":arguments.fieldID, "controllingReferenceType":"customField", 
				"referenceID":arguments.itemID, "referenceType":"cf_#arguments.fieldID#", "resourceType":"CustomField", "resourceTypeTitle":"", 
				"onDeleteImageHandler":arguments.deleteImageHandler, "onSaveImageHandler":arguments.saveImageHandler,  "header":'', "ftdImgClassList":"" 
			}]>
		<cfset local.strFeaturedImage = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.orgCode, siteCode=arguments.siteCode, arrConfigs=local.arrConfigs)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				#local.strFeaturedImage.js#
				#local.strFeaturedImage.html#
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFieldChanges" access="public" output="false" returntype="array">
		<cfargument name="arrFieldData" type="array" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="prependUsageName" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.arrFieldChanges = []>
		<cfset local.qryFieldResponses = getResponses(itemType=arguments.itemType, itemID=arguments.itemID)>
			
		<cfloop array="#arguments.arrFieldData#" index="local.cf">
			<cfquery name="local.qryThisFieldResponse" dbtype="query">
				SELECT valueID, customValue
				FROM [local].qryFieldResponses
				WHERE fieldID = #val(local.cf.fieldID)#
			</cfquery>
			
			<cfset local.thisFieldValueIDList = "">
			<cfset local.thisFieldValue = "">
			<cfif local.qryThisFieldResponse.recordCount>
				<cfset local.thisFieldValueIDList = valueList(local.qryThisFieldResponse.valueID)>
				<cfset local.thisFieldValue = valueList(local.qryThisFieldResponse.customValue)>
			</cfif>

			<cfset local.thisItem = local.cf.fieldText>
			<cfif len(arguments.prependUsageName)>
				<cfset local.thisItem = "[#arguments.prependUsageName#] #local.cf.fieldText#">
			</cfif>
			
			<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
				<cfif listSort(local.thisFieldValueIDList,'Numeric') NEQ listSort(local.cf.value,'Numeric')>
					<cfif listLen(local.cf.value)>
						<cfset local.qryNewFieldValues = getFieldOptions(fieldID=local.cf.fieldID, restrictToValueIDList=local.cf.value)>
						<cfset local.newFieldValues = local.qryNewFieldValues.recordCount GT 0 ? valueList(local.qryNewFieldValues.fieldValue) : "">
					<cfelse>
						<cfset local.newFieldValues = "">
					</cfif>
					<cfset local.thisChange = { ITEM=local.thisItem, OLDVALUE=local.thisFieldValue, NEWVALUE=local.newFieldValues }>
					<cfset arrayAppend(local.arrFieldChanges,local.thisChange)>
				</cfif>
			<cfelseif Compare(local.thisFieldValue,local.cf.value)>
				<cfset local.thisChange = { ITEM=local.thisItem, OLDVALUE=local.thisFieldValue, NEWVALUE=local.cf.value }>
				<cfset arrayAppend(local.arrFieldChanges,local.thisChange)>
			</cfif>
		</cfloop>

		<cfreturn local.arrFieldChanges>
	</cffunction>

</cfcomponent>