<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script language="javascript">
	var mccf_objdatatypecode = [];
	var mccf_objfieldtypesupportqty = [];
	var mccf_objfieldtypesupportamt = [];
	<cfloop query="local.qryCustomFieldTypes">
		mccf_objdatatypecode['#local.qryCustomFieldTypes.displayTypeCode#'] = [#left(local.qryCustomFieldTypes.fieldTypeCodes,len(local.qryCustomFieldTypes.fieldTypeCodes)-1)#];
		<cfif len(local.qryCustomFieldTypes.supportQtyFieldCodes)>
			mccf_objfieldtypesupportqty['#local.qryCustomFieldTypes.displayTypeCode#'] = ['#left(local.qryCustomFieldTypes.supportQtyFieldCodes,len(local.qryCustomFieldTypes.supportQtyFieldCodes)-1)#'];
		<cfelse>
			mccf_objfieldtypesupportqty['#local.qryCustomFieldTypes.displayTypeCode#'] = [''];
		</cfif>
		<cfif len(local.qryCustomFieldTypes.supportAmtFieldCodes)>
			mccf_objfieldtypesupportamt['#local.qryCustomFieldTypes.displayTypeCode#'] = ['#left(local.qryCustomFieldTypes.supportAmtFieldCodes,len(local.qryCustomFieldTypes.supportAmtFieldCodes)-1)#'];
		<cfelse>
			mccf_objfieldtypesupportamt['#local.qryCustomFieldTypes.displayTypeCode#'] = [''];
		</cfif>
	</cfloop>

	$(function() {
		mccf_showHideReqMsg();
		mccf_showHideFieldDetails('#local.qryCustomField.displayTypeCode#');

		$('##frmCustomField input').keyup(function(event) {
			if (event.keyCode == 13) {
				mccf_save_field('#arguments.gridExt#');
				return false;
			}
		});

		<cfif arguments.fieldID is 0 and local.qryCustomFieldTypes.recordCount is 1>
			$('##displayType').val($("##displayType option:last").val()).change();
		</cfif>

		<cfif arguments.fieldID gt 0 and local.qryFieldUsage.allowBranching is 1 and arrayLen(local.strBranchFieldFilters.arrFieldConditions)>
			var #toScript(local.strBranchFieldFilters.arrFieldConditions,"arrFieldConditions")#
			mccf_addFieldConditionsFromArr('#arguments.gridExt#',arrFieldConditions);
		</cfif>

		<!--- notify control app that the form is ready --->
		parent.postMessage({ success:true, messagetype:'MCCFFormLoadEvent', gridext:'#arguments.gridExt#' },'*');
	});
	</script>
	<script type="text/javascript" src="/assets/admin/javascript/resourceCustomFieldForm.js#local.assetCachingKey#"></script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<div id="divCustomForm">
	<form name="frmCustomField" id="frmCustomField" class="p-3" onsubmit="mccf_save_field('#arguments.gridExt#');return false;" autocomplete="off">
	<input type="hidden" name="fieldID" id="fieldID" value="#arguments.fieldID#">
	<input type="hidden" name="csrid" id="csrid" value="#arguments.csrid#">
	<input type="hidden" name="fieldTypeID" id="fieldTypeID" value="#local.qryCustomField.fieldTypeID#">
	<input type="hidden" name="usageID" id="usageID" value="#arguments.usageID#">
	<input type="hidden" name="parentUsageID" id="parentUsageID" value="#val(local.qryFieldUsage.parentUsageID)#">
	<input type="hidden" name="detailID" id="detailID" value="#arguments.detailID#">

	<div id="cf_field_err_div" class="alert alert-danger d-none"></div>

	<cfif local.qryFieldUsage.parentUsageID gt 0>
		<div class="form-label-group">
			<input type="text" name="typeOfFieldRO" id="typeOfFieldRO" value="#local.qryFieldUsage.areaDesc#" class="form-control" readonly>
			<label for="typeOfFieldRO">Type of <span class="mccf_fieldlabelsingle">Field</span></label>
		</div>
	</cfif>
	<cfif arguments.fieldID gt 0>
		<input type="hidden" name="displayType" id="displayType" value="#local.qryCustomField.displayTypeCode#">
		<input type="hidden" name="fieldType" id="fieldType" value="#local.qryCustomField.fieldTypeCode#">
		<div class="form-label-group">
			<input type="text" name="displayTypeRO" id="displayTypeRO" value="#local.qryCustomField.displayType#" class="form-control" readonly>
			<label for="displayTypeRO">Display <span class="mccf_fieldlabelsingle">Field</span> As</label>
		</div>
		<div class="form-label-group">
			<input type="text" name="fieldTypeRO" id="fieldTypeRO" value="#local.qryCustomField.dataTypeDisplayAs#" class="form-control" readonly>
			<label for="fieldTypeRO">Store Data As</label>
		</div>
	<cfelse>
		<div class="form-label-group">
			<select name="displayType" id="displayType" class="form-control" onChange="mccf_setDataTypeOptions(this.value);">
				<option value=""></option>
				<cfloop query="local.qryCustomFieldTypes">
					<option value="#local.qryCustomFieldTypes.displayTypeCode#">#local.qryCustomFieldTypes.displayType#</option>
				</cfloop>
			</select>
			<label for="displayType">Display <span class="mccf_fieldlabelsingle">Field</span> as *</label>
		</div>
		<div class="form-label-group">
			<select id="fieldType" name="fieldType" class="form-control" onchange="mccf_setFieldTypeDetails();"></select>
			<label for="fieldType">Data stored as *</label>
		</div>
	</cfif>
	<cfif arguments.fieldID gt 0 and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div class="form-label-group">
			<input type="text" name="fieldUID" id="fieldUID" value="#local.qryCustomField.uid#" autocomplete="off" class="form-control">
			<label for="fieldUID">UID *</label>
		</div>
	</cfif>

	<cfif local.hasFtdImageFieldTypeSupport>
		<div id="ftdImgConfigFieldContainer" class="form-group "<cfif local.qryCustomField.fieldTypeCode NEQ 'FEATUREDIMAGE'> style="display:none;"</cfif>>
			<div class="form-label-group">
				<select name="mccf_ftdImgConfigID" id="mccf_ftdImgConfigID" class="form-control">
					<option value="">Select a Featured Image Config</option>
					<cfoutput query="local.qryFeaturedImageConfigs" group="isPlatformWide">
						<cfif local.qrySiteFeaturedImageConfigs.recordCount>
							<optgroup label="<cfif local.qryFeaturedImageConfigs.isPlatformWide>System-Wide<cfelse>Site Specific</cfif>">
						</cfif>
						<cfoutput>
							<option value="#local.qryFeaturedImageConfigs.featureImageConfigID#"<cfif local.qryFeaturedImageConfigs.featureImageConfigID eq local.qryCustomField.featureImageConfigID> selected="selected"</cfif>>#local.qryFeaturedImageConfigs.featuredImageConfigName#</option>
						</cfoutput>
						<cfif local.qrySiteFeaturedImageConfigs.recordCount>
							</optgroup>
						</cfif>
					</cfoutput>
				</select>
				<label for="mccf_ftdImgConfigID">Image Configuration *</label>
			</div>
		</div>
	</cfif>

	<div class="form-label-group mt-3">
		<textarea name="fieldText" id="fieldText" rows="2" class="form-control">#local.qryCustomField.fieldText#</textarea>
		<label for="fieldUID">Screen Label * (Text only - no HTML)</label>
	</div>
	<div>
		<div class="form-check form-check-inline">
			<input type="checkbox" name="adminOnly" id="adminOnly" value="1" class="form-check-input" <cfif local.qryCustomField.adminOnly is 1> checked="checked"</cfif>>
			<label class="form-check-label" for="adminOnly">This is an admin only <span class="mccf_fieldlabelsingle">field</span></label>
		</div>
		<cfif local.qryFieldUsage.offerDisplayOnly is 1>
			<div class="form-check form-check-inline">
				<input type="checkbox" name="dispOnly" id="dispOnly" value="1" class="form-check-input" <cfif local.qryCustomField.displayOnly is 1> checked="checked"</cfif>>
				<label class="form-check-label" for="dispOnly">This is a display only <span class="mccf_fieldlabelsingle">field</span></label>
			</div>
		<cfelse>
			<span class="d-none"><input type="checkbox" name="dispOnly" id="dispOnly" value="1"></span>
		</cfif>
	</div>

	<div class="form-group" id="fieldNameAutoFill" <cfif local.qryCustomField.fieldTypeCode neq 'NAMETEXTBOX'>style="display:none;"</cfif>>
		<div class="form-check">
			<input type="checkbox" name="autoFillReg" id="autoFillReg" value="1" class="form-check-input" <cfif local.qryCustomField.autoFillRegistrant is 1> checked="checked"</cfif>>
			<label class="form-check-label" for="autoFillReg">Pre-populate with registrant's name where applicable</label>
		</div>
	</div>

	<div id="fieldDetails" class="mt-3">
		<div class="form-group row no-gutters align-items-start">
			<div class="col form-label-group mb-2">
				<input type="text" name="fieldReference" id="fieldReference" value="#local.qryCustomField.fieldReference#" class="form-control" maxlength="128" autocomplete="off" onBlur="mccf_checkFieldName();mccf_doesFieldExist('#arguments.gridExt#',#arguments.csrid#,#arguments.fieldID#,#arguments.usageID#,#arguments.detailID#);">
				<label for="fieldReference">Export Label *</label>
			</div>
			<span id="titleBox" class="col-auto pl-2">
				<span id="titleIcon"></span>
				<span id="titleText"></span>
			</span>
		</div>
		<div class="form-group">
			<div class="form-check form-check-inline">
				<input type="checkbox" name="isRequired" id="isRequired" value="1" class="form-check-input" <cfif local.qryCustomField.isRequired is 1> checked="checked"</cfif> onclick="mccf_showHideReqMsg();">
				<label class="form-check-label" for="isRequired">Is <span class="mccf_fieldlabelsingle">Field</span> Required?</label>
			</div>
		</div>
		<div class="form-group mt-2" id="requiredMessage" style="display:none;">
			<div class="form-label-group">
				<textarea name="requiredMsg" id="requiredMsg" class="form-control" maxlength="250" rows="2">#local.qryCustomField.requiredMsg#</textarea>
				<label for="requiredMsg">Required Message* (up to 250 characters, text only - no HTML)</label>
			</div>
		</div>
	</div>

	<div id="fieldGrouping">
		<div class="form-group mt-3">
			<div class="form-label-group">
				<select name="fldGrping" id="fldGrping" class="form-control" onchange="mccf_onChangeFieldGrouping(this.value);">
					<option value="0">Default - No Grouping</option>
					<cfloop query="local.qryFieldGroupings">
						<option value="#local.qryFieldGroupings.fieldGroupingID#"<cfif local.qryFieldGroupings.fieldGroupingID eq local.qryCustomField.fieldGroupingID> selected</cfif>>#local.qryFieldGroupings.fieldGrouping#</option>
					</cfloop>
					<option value="new">Add New Grouping</option>
				</select>
				<label for="fldGrping"><span class="mccf_fieldlabelsingle">Field</span> Grouping</label>
			</div>
		</div>
		<div class="form-group mt-2 newFieldGroupingRow d-none">
			<div class="form-label-group">
				<input type="text" name="newFldGrpingName" id="newFldGrpingName" value="" autocomplete="off" class="form-control" maxlength="200">
				<label for="newFldGrpingName">New <span class="mccf_fieldlabelsingle">Field</span> Grouping</label>
			</div>
		</div>
		<div class="form-group mt-2 newFieldGroupingRow d-none">
			<div class="form-label-group">
				<textarea name="newFldGrpingDesc" id="newFldGrpingDesc" class="form-control" maxlength="250" rows="2"></textarea>
				<label for="newFldGrpingDesc">New <span class="mccf_fieldlabelsingle">Field</span> Grouping Desc</label>
			</div>
		</div>
	</div>

	<div class="form-group mt-2" id="fieldQtyDetails" <cfif val(local.qryCustomField.supportQty) is 0>style="display:none;"</cfif>>
		<div class="form-label-group">
			<input type="text" name="inventory" id="inventory" value="#local.qryCustomField.inventory#" class="form-control">
			<label for="inventory">Max Available</label>
			<div class="ml-1">(optional - set this field to control inventory)</div>
		</div>
	</div>

	<div class="mt-2" id="fieldAmtDetails" <cfif NOT ( (val(local.qryCustomField.supportQty) is 1 and local.qryCustomField.displayTypeCode eq 'TEXTBOX') AND (local.qryFieldUsage.parentUsageID is 1 OR val(local.qryCustomField.supportAmt) is 1) )>style="display:none;"</cfif>>
		<cfif local.qryFieldUsage.parentUsageID is 1>
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">
						<cfif arguments.usageID is 11>Item Price and Availability *<cfelse>Item Availability *</cfif>
					</div>
					<cfif arguments.usageID is 11>
						<small class="ml-3">(Price will not appear if $0.00)</small>
					</cfif>
				</div>
				<div class="card-body p-3">
					<cfloop query="local.qryEventScheduleMappedPrice">
						<cfset local.thisScheduleAmt = NumberFormat(val(local.qryEventScheduleMappedPrice.amount),"0.00")>
						<div class="row">
							<div class="col-1 pr-1 text-right">
								<input type="checkbox" name="cf_customPrice_#local.qryEventScheduleMappedPrice.scheduleID#" id="cf_customPrice_#local.qryEventScheduleMappedPrice.scheduleID#" class="cf_customPrice" value="1" <cfif len(local.qryEventScheduleMappedPrice.amount)>checked="checked"</cfif>>							
							</div>
							<div class="col-11">
								<label class="form-check-label" for="cf_customPrice_#local.qryEventScheduleMappedPrice.scheduleID#">
									#local.qryEventScheduleMappedPrice.rangeName# - #DateFormat(local.qryEventScheduleMappedPrice.startDate,'m/d/yyyy')# #TimeFormat(local.qryEventScheduleMappedPrice.startDate,'h:mm tt')#
									to #DateFormat(local.qryEventScheduleMappedPrice.endDate,'m/d/yyyy')# #TimeFormat(local.qryEventScheduleMappedPrice.endDate,'h:mm tt')#
								</label>
							</div>
						</div>
						<div class="row<cfif local.qryEventScheduleMappedPrice.currentRow lt local.qryEventScheduleMappedPrice.recordCount> mb-3</cfif>">
							<div class="offset-1 col-11 mt-1">
								<div class="d-flex align-items-center px-lg-1">
									<cfif arguments.usageID is 11>
										<div class="input-group input-group-sm">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<input type="text" name="cf_customAmt_#local.qryEventScheduleMappedPrice.scheduleID#" id="cf_customAmt_#local.qryEventScheduleMappedPrice.scheduleID#" value="#local.thisScheduleAmt#" class="form-control form-control-sm" style="width:100px;" onBlur="this.value=formatCurrency(this.value);">
											<span class="ml-1">#local.displayedCurrencyType#</span>
										</div>
									<cfelse>
										<input type="hidden" name="cf_customAmt_#local.qryEventScheduleMappedPrice.scheduleID#" id="cf_customAmt_#local.qryEventScheduleMappedPrice.scheduleID#" value="0">
									</cfif>
								</div>
							</div>
						</div>
					</cfloop>
				</div>
			</div>
		<cfelse>
			<div class="form-group">
				<div class="input-group flex-nowrap">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<div class="form-label-group flex-grow-1 mb-0">
						<input type="text" name="amount" id="amount" value="#local.qryCustomField.amount#" class="form-control" onBlur="this.value=formatCurrency(this.value);">
						<label for="amount">Item Price *</label>
					</div>
				</div>
			</div>
		</cfif>
	</div>

	<div class="form-group mt-2" id="fieldRevAcctDetails" <cfif val(local.qryCustomField.supportAmt) is 0>style="display:none;"</cfif>>
		<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
		<input type="hidden" name="mccf_GLAccountID" id="mccf_GLAccountID" value="#val(local.qryCustomField.GLAccountID)#">
		<div class="form-label-group">
			<input type="text" id="mccf_GLAccountPath" value="<cfif len(local.GLAccountPath)>#local.GLAccountPath#<cfelse>(No account selected;)</cfif>" class="form-control" maxlength="20" readonly disabled="true">
			<label for="mccf_GLAccountPath">Revenue Account</label>
			<a href="javascript:mccf_selectGLAccount();">Choose GL Account</a> &nbsp; &bull; &nbsp; <a href="javascript:mccf_clearGLAccount();">Clear Selected GL Account</a><span id="glSaveMsg" class="font-weight-bold text-danger ml-3"></span>
		</div>
	</div>

	<cfif local.qryFieldUsage.allowBranching is 1>
		<div id="fieldConditions" class="my-2">
			<div class="form-group row">
				<label for="enableConditions" class="col-sm-4 col-xl-3 col-form-label-sm font-size-md">Enable Conditional Logic</label>
				<div class="col-sm-8 col-xl-9">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="enableConditions" id="enableConditions" value="1" class="custom-control-input" onclick="mccf_enableConditions('#arguments.gridExt#',this.checked);"<cfif local.qryCustomField.enableConditions is 1> checked="checked"</cfif>>
						<label class="custom-control-label" for="enableConditions"></label>
					</div>
				</div>
			</div>			
			<div class="form-group row conditionFieldRow" <cfif val(local.qryCustomField.enableConditions) is 0> style="display:none;"</cfif>>
				<div class="col-12">
					<div class="my-2">Show this <span class="mccf_fieldlabelsingle">Field</span> if:</div>
				</div>
				<div class="col-12">
					<cfif local.strBranchFieldFilters.qryFields.recordcount>
						#local.strBranchFieldFilters.fieldSelectArea#
					<cfelse>
						<i>(no conditions defined)</i>
					</cfif>
				</div>
			</div>
		</div>
	</cfif>
	<!--- hidden submit triggered from parent --->
	<button type="submit" class="d-none"></button>
	</form>
</div>
#local.showGLSelector.data#

<div id="saveCFloadingDIV" style="display:none;">
	<div class="text-center">
		<div class="spinner-border mt-3" role="status">
			<span class="sr-only">Loading...</span>
		</div>
		<div class="font-weight-bold mt-4">Please wait while we validate and save the <span class="mccf_fieldlabelsingle">field</span>.</div>
	</div>
</div>

<cfif local.qryFieldUsage.allowBranching is 1>
	#local.strBranchFieldFilters.fieldSelectControls#
</cfif>
</cfoutput>